#Requires -Version 5.1
<#
================================================================================
Windows Server 2019 Security Audit Script
================================================================================
Description: Enhanced audit script for Windows Server 2019 security configuration assessment
             with hardware fingerprinting, tamper detection, field obfuscation, and encryption
Version: 2.1 (Windows Server 2019/2016/2022 Compatible)
Created: August 17, 2025
Updated: January 9, 2025 - Added technical review enhancements

INSTRUCTIONS FOR ADMIN:
1. Copy this file to the Windows Server 2019 system
2. Run PowerShell as Administrator and execute:

   .\Windows-Server-2019-Security-Audit.ps1

   OR with custom output location:
   .\Windows-Server-2019-Security-Audit.ps1 -OutputPath "C:\Temp\Windows-Security-Results.json"

3. Send the generated JSON file back for analysis

CRITICAL: This script is 100% READ-ONLY and safe for production environments
All operations use only Get-* cmdlets, WMI queries, and read-only registry operations
================================================================================
#>

[CmdletBinding()]
param(
    [Parameter(Mandatory = $false)]
    [ValidateScript({
        $ParentPath = Split-Path $_ -Parent
        if ($ParentPath -and -not (Test-Path $ParentPath -PathType Container)) {
            throw "Output directory does not exist: $ParentPath"
        }
        $true
    })]
    [string]$OutputPath = ".\Windows-Server-2019-Security-Results.json",

    [Parameter(Mandatory = $false)]
    [switch]$EncryptOutput,

    [Parameter(Mandatory = $false)]
    [string]$EncryptionPassword = $null,

    [Parameter(Mandatory = $false)]
    [ValidateSet("AES256", "AES128", "TripleDES")]
    [string]$EncryptionAlgorithm = "AES256"
)

# Initialize audit session
$AuditStartTime = Get-Date
$AuditID = [System.Guid]::NewGuid()
$AuditResults = @{}

# =============================================================================
# AUDIT FIELD OBFUSCATION MAPPINGS
# =============================================================================

# Obfuscated field mappings to protect audit methodology from auditee analysis
$ObfuscationMap = @{
    # Main sections
    'SystemIdentity' = 'Section_A7B2'
    'AuditSignature' = 'Meta_X9K1'
    'ValidationInstructions' = 'Guide_P4M8'
    'AuditMetadata' = 'Legacy_D3F5'
    'ComplianceSummary' = 'Summary_K8L9'

    # Security controls
    'WIN_1_1_WindowsUpdate' = 'Control_W1M2'
    'WIN_2_1_UserAccounts' = 'Control_U2N3'
    'WIN_3_1_WindowsFirewall' = 'Control_F3P4'
    'WIN_4_1_AuditLogging' = 'Control_L4Q5'
    'WIN_5_1_ServiceSecurity' = 'Control_S5R6'
    'WIN_6_1_RegistrySecurity' = 'Control_R6T7'
    'WIN_7_1_FileSystemSecurity' = 'Control_F7U8'

    # SystemIdentity fields
    'ComputerName' = 'Sys_ID_001'
    'FQDN' = 'Net_ID_002'
    'MachineGUID' = 'Mach_ID_003'
    'SystemUUID' = 'UUID_ID_004'
    'SerialNumber' = 'Serial_005'
    'NetworkAdapters' = 'Net_Adapt_006'
    'IPAddresses' = 'IP_List_007'
    'DomainRole' = 'Dom_Role_008'
    'Domain' = 'Domain_009'
    'Workgroup' = 'WGroup_010'
    'HardwareFingerprint' = 'HW_Print_011'
    'CompositeHardwareFingerprint' = 'CHF_Hash_012'
    'IntegrityMetadata' = 'Int_Meta_013'
    'IntegrityHashes' = 'Int_Hash_014'

    # Hardware components
    'CPU' = 'HW_CPU_001'
    'Motherboard' = 'HW_MB_002'
    'Memory' = 'HW_MEM_003'
    'Storage' = 'HW_STOR_004'
    'NetworkHardware' = 'HW_NET_005'

    # CPU fields
    'ProcessorId' = 'CPU_ID_001'
    'Signature' = 'CPU_SIG_002'
    'Family' = 'CPU_FAM_003'
    'Model' = 'CPU_MOD_004'
    'Stepping' = 'CPU_STEP_005'
    'Manufacturer' = 'CPU_MFG_006'
    'MaxClockSpeed' = 'CPU_CLK_007'
    'NumberOfCores' = 'CPU_CORE_008'
    'NumberOfLogicalProcessors' = 'CPU_LOG_009'

    # Motherboard fields
    'BaseBoardSerialNumber' = 'MB_SN_001'
    'BaseBoardProduct' = 'MB_PROD_002'
    'BaseBoardManufacturer' = 'MB_MFG_003'
    'BaseBoardVersion' = 'MB_VER_004'
    'BIOSSerialNumber' = 'BIOS_SN_005'
    'BIOSVersion' = 'BIOS_VER_006'
    'BIOSManufacturer' = 'BIOS_MFG_007'
    'BIOSReleaseDate' = 'BIOS_DATE_008'

    # Memory fields
    'PartNumber' = 'MEM_PN_002'
    'Capacity' = 'MEM_CAP_004'
    'Speed' = 'MEM_SPD_005'
    'DeviceLocator' = 'MEM_LOC_006'

    # Storage fields
    'FirmwareRevision' = 'STOR_FW_005'
    'InterfaceType' = 'STOR_INT_006'
    'Size' = 'STOR_SIZE_004'
    'StorageModel' = 'STOR_MOD_002'

    # Network hardware fields
    'MACAddress' = 'NET_MAC_001'
    'PNPDeviceID' = 'NET_PNP_002'
    'ProductName' = 'NET_PROD_004'
    'ServiceName' = 'NET_SVC_005'
    'Name' = 'Name_001'

    # Common fields used across multiple contexts
    'Enabled' = 'Enabled_003'
    'Status' = 'Status_004'
    'Description' = 'Desc_005'
    'DisplayName' = 'Display_006'
    'StartType' = 'StartType_007'
    'State' = 'State_008'
    'StartName' = 'StartName_009'
    'Path' = 'Path_010'
    'Value' = 'Value_011'
    'Error' = 'Error_012'

    # Integrity metadata fields
    'CollectionTimestamp' = 'TS_001'
    'CollectedBy' = 'BY_002'
    'PowerShellVersion' = 'PS_VER_003'
    'OSVersion' = 'OS_VER_004'
    'OSBuildNumber' = 'OS_BUILD_005'
    'InstallDate' = 'INST_DATE_006'
    'LastBootUpTime' = 'BOOT_TIME_007'
    'TimeZone' = 'TZ_008'
    'SystemUptime' = 'UPTIME_009'

    # Hash fields
    'SHA256' = 'Hash_SHA256_001'
    'SHA512' = 'Hash_SHA512_002'
    'MD5' = 'Hash_MD5_003'
    'CRC32' = 'Hash_CRC32_004'
    'DataLength' = 'Data_Len_005'
    'Algorithm' = 'Algo_006'
    'Timestamp' = 'TS_007'

    # Audit signature fields
    'AuditIntegrityHashes' = 'Sig_Hash_001'
    'SignatureTimestamp' = 'Sig_TS_002'
    'SignedBy' = 'Sig_By_003'
    'AuditVersion' = 'Audit_Ver_004'
    'IntegrityAlgorithms' = 'Int_Algo_005'

    # Validation instruction fields
    'Usage' = 'Usage_001'
    'Example' = 'Example_002'
    'Warning' = 'Warning_003'
    'HardwareVerification' = 'HW_Verify_004'
    'Note' = 'Note_005'

    # Common control fields
    'ControlID' = 'Ctrl_ID_001'
    'ControlName' = 'Ctrl_Name_002'
    'RiskLevel' = 'Risk_Level_003'
    'CVSSScore' = 'CVSS_004'
    'CurrentValue' = 'Current_005'
    'BaselineValue' = 'Baseline_006'
    'ComplianceStatus' = 'Compliance_007'
    'ComplianceScore' = 'Score_008'
    'Finding' = 'Finding_009'
    'Recommendation' = 'Recommend_010'
    'AssessmentDate' = 'Assess_Date_011'
}

function ConvertTo-ObfuscatedHashtable {
    param([hashtable]$InputHashtable)

    $ObfuscatedHashtable = @{}

    foreach ($Key in $InputHashtable.Keys) {
        $ObfuscatedKey = if ($ObfuscationMap.ContainsKey($Key)) { $ObfuscationMap[$Key] } else { $Key }
        $Value = $InputHashtable[$Key]

        # Recursively obfuscate nested hashtables
        if ($Value -is [hashtable]) {
            $ObfuscatedHashtable[$ObfuscatedKey] = ConvertTo-ObfuscatedHashtable -InputHashtable $Value
        }
        # Handle arrays of hashtables
        elseif ($Value -is [array] -and $Value.Count -gt 0 -and $Value[0] -is [hashtable]) {
            $ObfuscatedHashtable[$ObfuscatedKey] = @()
            foreach ($Item in $Value) {
                if ($Item -is [hashtable]) {
                    $ObfuscatedHashtable[$ObfuscatedKey] += ConvertTo-ObfuscatedHashtable -InputHashtable $Item
                } else {
                    $ObfuscatedHashtable[$ObfuscatedKey] += $Item
                }
            }
        }
        else {
            $ObfuscatedHashtable[$ObfuscatedKey] = $Value
        }
    }

    return $ObfuscatedHashtable
}

# =============================================================================
# JSON ENCRYPTION FUNCTIONS
# =============================================================================

function New-EncryptionKey {
    param(
        [string]$Password,
        [string]$Algorithm = "AES256"
    )

    # Generate salt for key derivation
    $Salt = New-Object byte[] 16
    $RNG = [System.Security.Cryptography.RNGCryptoServiceProvider]::Create()
    $RNG.GetBytes($Salt)
    $RNG.Dispose()

    # Derive key from password using PBKDF2
    $KeySize = switch ($Algorithm) {
        "AES256" { 32 }
        "AES128" { 16 }
        "TripleDES" { 24 }
        default { 32 }
    }

    $PBKDF2 = New-Object System.Security.Cryptography.Rfc2898DeriveBytes($Password, $Salt, 10000)
    $Key = $PBKDF2.GetBytes($KeySize)
    $PBKDF2.Dispose()

    return @{
        Key = $Key
        Salt = $Salt
        Algorithm = $Algorithm
    }
}

function Protect-JsonData {
    param(
        [string]$JsonData,
        [string]$Password,
        [string]$Algorithm = "AES256"
    )

    try {
        # Generate encryption key
        $KeyData = New-EncryptionKey -Password $Password -Algorithm $Algorithm

        # Create cipher
        $Cipher = switch ($Algorithm) {
            "AES256" { [System.Security.Cryptography.Aes]::Create() }
            "AES128" { [System.Security.Cryptography.Aes]::Create() }
            "TripleDES" { [System.Security.Cryptography.TripleDES]::Create() }
            default { [System.Security.Cryptography.Aes]::Create() }
        }

        $Cipher.Key = $KeyData.Key
        $Cipher.GenerateIV()

        # Encrypt the JSON data
        $Encryptor = $Cipher.CreateEncryptor()
        $JsonBytes = [System.Text.Encoding]::UTF8.GetBytes($JsonData)

        $MemoryStream = New-Object System.IO.MemoryStream
        $CryptoStream = New-Object System.Security.Cryptography.CryptoStream($MemoryStream, $Encryptor, [System.Security.Cryptography.CryptoStreamMode]::Write)

        $CryptoStream.Write($JsonBytes, 0, $JsonBytes.Length)
        $CryptoStream.FlushFinalBlock()

        $EncryptedBytes = $MemoryStream.ToArray()

        # Clean up
        $CryptoStream.Dispose()
        $MemoryStream.Dispose()
        $Encryptor.Dispose()

        # Create encrypted package
        $EncryptedPackage = @{
            EncryptedData = [System.Convert]::ToBase64String($EncryptedBytes)
            IV = [System.Convert]::ToBase64String($Cipher.IV)
            Salt = [System.Convert]::ToBase64String($KeyData.Salt)
            Algorithm = $Algorithm
            KeyDerivationIterations = 10000
            EncryptionTimestamp = Get-Date -Format "yyyy-MM-ddTHH:mm:ss.fffZ"
            EncryptedBy = [System.Security.Principal.WindowsIdentity]::GetCurrent().Name
            DataLength = $JsonBytes.Length
            EncryptedLength = $EncryptedBytes.Length
        }

        $Cipher.Dispose()

        return $EncryptedPackage
    }
    catch {
        Write-Host "[ERROR] Encryption failed: $($_.Exception.Message)" -ForegroundColor Red
        throw
    }
}

function Unprotect-JsonData {
    param(
        [hashtable]$EncryptedPackage,
        [string]$Password
    )

    try {
        # Recreate encryption key using stored salt
        $Salt = [System.Convert]::FromBase64String($EncryptedPackage.Salt)
        $KeySize = switch ($EncryptedPackage.Algorithm) {
            "AES256" { 32 }
            "AES128" { 16 }
            "TripleDES" { 24 }
            default { 32 }
        }

        $PBKDF2 = New-Object System.Security.Cryptography.Rfc2898DeriveBytes($Password, $Salt, $EncryptedPackage.KeyDerivationIterations)
        $Key = $PBKDF2.GetBytes($KeySize)
        $PBKDF2.Dispose()

        # Create cipher for decryption
        $Cipher = switch ($EncryptedPackage.Algorithm) {
            "AES256" { [System.Security.Cryptography.Aes]::Create() }
            "AES128" { [System.Security.Cryptography.Aes]::Create() }
            "TripleDES" { [System.Security.Cryptography.TripleDES]::Create() }
            default { [System.Security.Cryptography.Aes]::Create() }
        }

        $Cipher.Key = $Key
        $Cipher.IV = [System.Convert]::FromBase64String($EncryptedPackage.IV)

        # Decrypt the data
        $EncryptedBytes = [System.Convert]::FromBase64String($EncryptedPackage.EncryptedData)
        $Decryptor = $Cipher.CreateDecryptor()

        $MemoryStream = New-Object System.IO.MemoryStream($EncryptedBytes)
        $CryptoStream = New-Object System.Security.Cryptography.CryptoStream($MemoryStream, $Decryptor, [System.Security.Cryptography.CryptoStreamMode]::Read)

        $DecryptedBytes = New-Object byte[] $EncryptedPackage.DataLength
        $CryptoStream.Read($DecryptedBytes, 0, $DecryptedBytes.Length)

        $DecryptedJson = [System.Text.Encoding]::UTF8.GetString($DecryptedBytes)

        # Clean up
        $CryptoStream.Dispose()
        $MemoryStream.Dispose()
        $Decryptor.Dispose()
        $Cipher.Dispose()

        return $DecryptedJson
    }
    catch {
        Write-Host "[ERROR] Decryption failed: $($_.Exception.Message)" -ForegroundColor Red
        throw
    }
}

# =============================================================================
# TAMPER DETECTION AND INTEGRITY FUNCTIONS
# =============================================================================

function New-SystemIntegrityHash {
    param([hashtable]$SystemData)

    # Create canonical JSON representation
    $CanonicalJson = ($SystemData | ConvertTo-Json -Depth 20 -Compress)

    # Multiple hash algorithms using built-in .NET classes
    $Hashes = @{}

    # SHA256
    $SHA256 = [System.Security.Cryptography.SHA256]::Create()
    $SHA256Bytes = $SHA256.ComputeHash([System.Text.Encoding]::UTF8.GetBytes($CanonicalJson))
    $Hashes.SHA256 = [System.Convert]::ToBase64String($SHA256Bytes)
    $SHA256.Dispose()

    # SHA512
    $SHA512 = [System.Security.Cryptography.SHA512]::Create()
    $SHA512Bytes = $SHA512.ComputeHash([System.Text.Encoding]::UTF8.GetBytes($CanonicalJson))
    $Hashes.SHA512 = [System.Convert]::ToBase64String($SHA512Bytes)
    $SHA512.Dispose()

    # MD5 (for quick validation)
    $MD5 = [System.Security.Cryptography.MD5]::Create()
    $MD5Bytes = $MD5.ComputeHash([System.Text.Encoding]::UTF8.GetBytes($CanonicalJson))
    $Hashes.MD5 = [System.Convert]::ToBase64String($MD5Bytes)
    $MD5.Dispose()

    # Simple CRC32 using built-in methods
    $CRC32 = 0xFFFFFFFF
    $Bytes = [System.Text.Encoding]::UTF8.GetBytes($CanonicalJson)
    foreach ($Byte in $Bytes) {
        $CRC32 = $CRC32 -bxor $Byte
        for ($i = 0; $i -lt 8; $i++) {
            if ($CRC32 -band 1) {
                $CRC32 = ($CRC32 -shr 1) -bxor 0xEDB88320
            } else {
                $CRC32 = $CRC32 -shr 1
            }
        }
    }
    $Hashes.CRC32 = (-bnot $CRC32).ToString("X8")

    $Hashes.DataLength = $CanonicalJson.Length
    $Hashes.Algorithm = "Multi-Layer-Builtin"
    $Hashes.Timestamp = Get-Date -Format "yyyy-MM-ddTHH:mm:ss.fffZ"

    return $Hashes
}

function Test-SystemIntegrity {
    param(
        [hashtable]$CurrentSystemData,
        [hashtable]$StoredHashes
    )

    $CurrentHashes = New-SystemIntegrityHash -SystemData $CurrentSystemData

    $IntegrityResults = @{
        IntegrityVerified = $true
        ValidationResults = @{}
        ValidationTimestamp = Get-Date -Format "yyyy-MM-ddTHH:mm:ss.fffZ"
    }

    foreach ($Algorithm in @('SHA256', 'SHA512', 'MD5', 'CRC32')) {
        $Match = $StoredHashes.$Algorithm -eq $CurrentHashes.$Algorithm
        $IntegrityResults.ValidationResults[$Algorithm] = @{
            Match = $Match
            StoredValue = $StoredHashes.$Algorithm
            CurrentValue = $CurrentHashes.$Algorithm
        }

        if (-not $Match) {
            $IntegrityResults.IntegrityVerified = $false
        }
    }

    return $IntegrityResults
}

# Constants for thresholds and intervals
$CRITICAL_SERVICES = @("Windows Update", "Windows Firewall", "Security Center", "Windows Event Log")
$CRITICAL_DIRECTORIES = @("C:\Windows\System32", "C:\Windows\SysWOW64", "C:\Program Files", "C:\Program Files (x86)")

# Compliance thresholds
$COMPLIANCE_THRESHOLDS = @{
    CriticalServices = 0.8
    RegistrySettings = 0.8
    MinPasswordLength = 8
}

Write-Host "=================================================================================" -ForegroundColor Green
Write-Host "Windows Server 2019 Security Audit" -ForegroundColor Green
Write-Host "=================================================================================" -ForegroundColor Green
Write-Host "Audit ID: $AuditID" -ForegroundColor Cyan
Write-Host "Started: $($AuditStartTime.ToString('yyyy-MM-dd HH:mm:ss'))" -ForegroundColor Cyan
Write-Host "Output: $OutputPath" -ForegroundColor Cyan
Write-Host "=================================================================================" -ForegroundColor Green
Write-Host ""

# Helper functions for safer operations
function Get-SafeProperty {
    param(
        [object]$Object,
        [string]$PropertyName,
        [object]$DefaultValue = "Unknown"
    )

    if ($Object -and $Object.PSObject.Properties[$PropertyName]) {
        return $Object.$PropertyName
    } else {
        return $DefaultValue
    }
}

function Get-SafeWmiObject {
    param(
        [string]$ClassName,
        [string]$Namespace = "root\cimv2"
    )

    try {
        # Check if WMI service is running
        $WmiService = Get-Service -Name "Winmgmt" -ErrorAction SilentlyContinue
        if ($WmiService -and $WmiService.Status -eq "Running") {
            return Get-WmiObject -Class $ClassName -Namespace $Namespace -ErrorAction Stop
        } else {
            Write-Host "[WARNING] WMI service not available for $ClassName query" -ForegroundColor Yellow
            return $null
        }
    } catch {
        Write-Host "[WARNING] WMI query failed for $ClassName : $($_.Exception.Message)" -ForegroundColor Yellow
        return $null
    }
}

function Get-SafePasswordPolicy {
    $PasswordPolicy = @{}

    try {
        # Try net accounts first
        $NetAccounts = & net accounts 2>$null
        if ($LASTEXITCODE -eq 0 -and $NetAccounts) {
            foreach ($line in $NetAccounts) {
                # Use more flexible regex patterns
                if ($line -match "Minimum password length[:\s]+(\d+)") {
                    $PasswordPolicy.MinPasswordLength = $matches[1]
                }
                if ($line -match "Maximum password age[:\s\(]+(\d+|Never)") {
                    $PasswordPolicy.MaxPasswordAge = $matches[1]
                }
                if ($line -match "Minimum password age[:\s\(]+(\d+)") {
                    $PasswordPolicy.MinPasswordAge = $matches[1]
                }
                if ($line -match "Password history[:\s]+(\d+)") {
                    $PasswordPolicy.PasswordHistory = $matches[1]
                }
                if ($line -match "Lockout threshold[:\s]+(\d+|Never)") {
                    $PasswordPolicy.LockoutThreshold = $matches[1]
                }
            }
        } else {
            $PasswordPolicy.Error = "net accounts command failed or not available"
        }
    } catch {
        $PasswordPolicy.Error = "Exception accessing password policy: $($_.Exception.Message)"
    }

    # Fallback: Try registry approach for critical settings
    if (-not $PasswordPolicy.MinPasswordLength) {
        try {
            $RegValue = Get-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Services\Netlogon\Parameters" -Name "MinimumPasswordLength" -ErrorAction SilentlyContinue
            if ($RegValue) {
                $PasswordPolicy.MinPasswordLength = $RegValue.MinimumPasswordLength
                $PasswordPolicy.Source = "Registry"
            }
        } catch {
            # Silent fallback
        }
    }

    return $PasswordPolicy
}

function Get-SafeAuditPolicy {
    $AuditPolicy = @{}

    try {
        # Check if auditpol is available
        $AuditPolPath = Get-Command auditpol.exe -ErrorAction SilentlyContinue
        if ($AuditPolPath) {
            $AuditPolicyOutput = & auditpol /get /category:* 2>$null
            if ($LASTEXITCODE -eq 0 -and $AuditPolicyOutput) {
                $AuditPolicy.Available = $true
                $AuditPolicy.Output = $AuditPolicyOutput | Select-Object -First 20
            } else {
                $AuditPolicy.Available = $false
                $AuditPolicy.Error = "auditpol command failed"
            }
        } else {
            $AuditPolicy.Available = $false
            $AuditPolicy.Error = "auditpol command not found"
        }
    } catch {
        $AuditPolicy.Available = $false
        $AuditPolicy.Error = "Exception accessing audit policy: $($_.Exception.Message)"
    }

    return $AuditPolicy
}

function Test-Prerequisites {
    $PreflightResults = @{
        IsAdmin = $false
        WmiAvailable = $false
        PowerShellVersion = $PSVersionTable.PSVersion.ToString()
        Warnings = @()
    }

    # Check Administrator privileges
    try {
        $CurrentPrincipal = New-Object Security.Principal.WindowsPrincipal([Security.Principal.WindowsIdentity]::GetCurrent())
        $PreflightResults.IsAdmin = $CurrentPrincipal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
    } catch {
        $PreflightResults.Warnings += "Cannot verify Administrator status"
    }

    # Check WMI availability
    try {
        $WmiService = Get-Service -Name "Winmgmt" -ErrorAction SilentlyContinue
        $PreflightResults.WmiAvailable = ($WmiService -and $WmiService.Status -eq "Running")
    } catch {
        $PreflightResults.Warnings += "Cannot verify WMI service status"
    }

    # Check PowerShell version
    if ($PSVersionTable.PSVersion.Major -lt 5) {
        $PreflightResults.Warnings += "PowerShell version may be too old for full compatibility"
    }

    return $PreflightResults
}

try {
    # =============================================================================
    # ENHANCED SYSTEM IDENTITY WITH HARDWARE FINGERPRINTING
    # =============================================================================

    Write-Host "Step 1/9: Collecting enhanced system identity and hardware fingerprint..." -ForegroundColor Yellow

    try {
        # Enhanced SystemIdentity with Hardware Fingerprinting and Tamper Detection
        $SystemIdentity = @{
            # Original System Identity
            ComputerName = $env:COMPUTERNAME
            FQDN = $(try { [System.Net.Dns]::GetHostByName($env:COMPUTERNAME).HostName } catch { $env:COMPUTERNAME })
            MachineGUID = (Get-ItemProperty "HKLM:\SOFTWARE\Microsoft\Cryptography" -ErrorAction SilentlyContinue).MachineGuid
            SystemUUID = (Get-CimInstance -Class Win32_ComputerSystemProduct -ErrorAction SilentlyContinue).UUID
            SerialNumber = (Get-CimInstance -Class Win32_BIOS -ErrorAction SilentlyContinue).SerialNumber
            NetworkAdapters = Get-NetAdapter -ErrorAction SilentlyContinue | Select-Object Name, MacAddress, InterfaceDescription
            IPAddresses = Get-NetIPAddress -ErrorAction SilentlyContinue | Where-Object {$_.AddressFamily -eq 'IPv4' -and $_.IPAddress -ne '127.0.0.1'}
            DomainRole = (Get-CimInstance -Class Win32_ComputerSystem -ErrorAction SilentlyContinue).DomainRole
            Domain = (Get-CimInstance -Class Win32_ComputerSystem -ErrorAction SilentlyContinue).Domain
            Workgroup = (Get-CimInstance -Class Win32_ComputerSystem -ErrorAction SilentlyContinue).Workgroup

            # Hardware Fingerprint Components
            HardwareFingerprint = @{
                # CPU Signature - Cache CIM query for efficiency
                CPU = @{
                    ProcessorId = ($CPUInfo = Get-CimInstance -Class Win32_Processor -ErrorAction SilentlyContinue)[0].ProcessorId
                    Signature = $CPUInfo[0].Signature
                    Family = $CPUInfo[0].Family
                    Model = $CPUInfo[0].Model
                    Stepping = $CPUInfo[0].Stepping
                    Manufacturer = $CPUInfo[0].Manufacturer
                    MaxClockSpeed = $CPUInfo[0].MaxClockSpeed
                    NumberOfCores = $CPUInfo[0].NumberOfCores
                    NumberOfLogicalProcessors = $CPUInfo[0].NumberOfLogicalProcessors
                }

                # Motherboard & BIOS Signature - Cache CIM queries for efficiency
                Motherboard = @{
                    BaseBoardSerialNumber = ($BaseBoardInfo = Get-CimInstance -Class Win32_BaseBoard -ErrorAction SilentlyContinue).SerialNumber
                    BaseBoardProduct = $BaseBoardInfo.Product
                    BaseBoardManufacturer = $BaseBoardInfo.Manufacturer
                    BaseBoardVersion = $BaseBoardInfo.Version
                    BIOSSerialNumber = ($BIOSInfo = Get-CimInstance -Class Win32_BIOS -ErrorAction SilentlyContinue).SerialNumber
                    BIOSVersion = $BIOSInfo.SMBIOSBIOSVersion
                    BIOSManufacturer = $BIOSInfo.Manufacturer
                    BIOSReleaseDate = $BIOSInfo.ReleaseDate
                }

                # Memory Signature
                Memory = @(
                    Get-CimInstance -Class Win32_PhysicalMemory -ErrorAction SilentlyContinue | ForEach-Object {
                        @{
                            SerialNumber = $_.SerialNumber
                            PartNumber = $_.PartNumber
                            Manufacturer = $_.Manufacturer
                            Capacity = $_.Capacity
                            Speed = $_.Speed
                            DeviceLocator = $_.DeviceLocator
                        }
                    }
                )

                # Storage Signature
                Storage = @(
                    Get-CimInstance -Class Win32_DiskDrive -ErrorAction SilentlyContinue | ForEach-Object {
                        @{
                            SerialNumber = $_.SerialNumber
                            StorageModel = $_.Model
                            Manufacturer = $_.Manufacturer
                            Size = $_.Size
                            FirmwareRevision = $_.FirmwareRevision
                            InterfaceType = $_.InterfaceType
                        }
                    }
                )

                # Network Hardware Signature
                NetworkHardware = @(
                    Get-CimInstance -Class Win32_NetworkAdapter -ErrorAction SilentlyContinue | Where-Object {$_.PhysicalAdapter -eq $true} | ForEach-Object {
                        @{
                            MACAddress = $_.MACAddress
                            PNPDeviceID = $_.PNPDeviceID
                            Manufacturer = $_.Manufacturer
                            ProductName = $_.ProductName
                            ServiceName = $_.ServiceName
                        }
                    }
                )
            }

            # Composite Hardware Fingerprint (SHA256 hash of all hardware components)
            CompositeHardwareFingerprint = $null  # Will be calculated below

            # System Integrity Information
            IntegrityMetadata = @{
                CollectionTimestamp = Get-Date -Format "yyyy-MM-ddTHH:mm:ss.fffZ"
                CollectedBy = [System.Security.Principal.WindowsIdentity]::GetCurrent().Name
                PowerShellVersion = $PSVersionTable.PSVersion.ToString()
                OSVersion = (Get-CimInstance -Class Win32_OperatingSystem -ErrorAction SilentlyContinue).Caption
                OSBuildNumber = (Get-CimInstance -Class Win32_OperatingSystem -ErrorAction SilentlyContinue).BuildNumber
                InstallDate = (Get-CimInstance -Class Win32_OperatingSystem -ErrorAction SilentlyContinue).InstallDate
                LastBootUpTime = (Get-CimInstance -Class Win32_OperatingSystem -ErrorAction SilentlyContinue).LastBootUpTime
                TimeZone = (Get-TimeZone -ErrorAction SilentlyContinue).Id
                SystemUptime = ((Get-Date) - (Get-CimInstance -Class Win32_OperatingSystem -ErrorAction SilentlyContinue).LastBootUpTime).TotalHours
            }
        }

        # Calculate Composite Hardware Fingerprint using built-in .NET classes
        $HardwareString = ($SystemIdentity.HardwareFingerprint | ConvertTo-Json -Depth 10 -Compress)
        $SHA256 = [System.Security.Cryptography.SHA256]::Create()
        $HashBytes = $SHA256.ComputeHash([System.Text.Encoding]::UTF8.GetBytes($HardwareString))
        $SystemIdentity.CompositeHardwareFingerprint = [System.Convert]::ToBase64String($HashBytes)
        $SHA256.Dispose()

        # Generate initial integrity hashes for the SystemIdentity
        $SystemIdentity.IntegrityHashes = New-SystemIntegrityHash -SystemData $SystemIdentity

        # Add validation instructions
        $SystemIdentity.ValidationInstructions = @{
            Usage = "Use Test-SystemIntegrity function to validate system identity integrity"
            Example = 'Test-SystemIntegrity -CurrentSystemData $SystemIdentity -StoredHashes $SystemIdentity.IntegrityHashes'
            Note = "Compare hardware fingerprints to verify this is the same physical machine"
        }

        $AuditResults.Add("SystemIdentity", $SystemIdentity)
        Write-Host "[SUCCESS] Enhanced system identity and hardware fingerprint collected" -ForegroundColor Green
        Write-Host "          Hardware Fingerprint: $($SystemIdentity.CompositeHardwareFingerprint.Substring(0,32))..." -ForegroundColor Cyan
        Write-Host "          CPU ID: $($SystemIdentity.HardwareFingerprint.CPU.ProcessorId)" -ForegroundColor Cyan
        Write-Host "          Motherboard Serial: $($SystemIdentity.HardwareFingerprint.Motherboard.BaseBoardSerialNumber)" -ForegroundColor Cyan
    }
    catch {
        Write-Host "[ERROR] Failed to collect enhanced system identity: $($_.Exception.Message)" -ForegroundColor Red
        $AuditResults.Add("SystemIdentity", @{ Error = $_.Exception.Message })
    }

    # =============================================================================
    # LEGACY AUDIT METADATA (for backward compatibility)
    # =============================================================================

    Write-Host "Step 2/9: Collecting legacy audit metadata for compatibility..." -ForegroundColor Yellow

    try {
        $ComputerInfo = Get-ComputerInfo -ErrorAction Stop
        $OSInfo = Get-SafeWmiObject -ClassName "Win32_OperatingSystem"
        $SystemInfo = Get-SafeWmiObject -ClassName "Win32_ComputerSystem"

        $AuditMetadata = @{
            AuditID = $AuditID
            AuditStartTime = $AuditStartTime
            ComputerName = $env:COMPUTERNAME
            WindowsVersion = Get-SafeProperty -Object $ComputerInfo -PropertyName "WindowsVersion"
            WindowsBuildNumber = Get-SafeProperty -Object $ComputerInfo -PropertyName "WindowsBuildLabEx"
            OSCaption = Get-SafeProperty -Object $OSInfo -PropertyName "Caption"
            OSArchitecture = Get-SafeProperty -Object $OSInfo -PropertyName "OSArchitecture"
            TotalPhysicalMemory = if ($ComputerInfo -and $ComputerInfo.TotalPhysicalMemory) { [math]::Round($ComputerInfo.TotalPhysicalMemory / 1GB, 2) } else { "Unknown" }
            Domain = Get-SafeProperty -Object $SystemInfo -PropertyName "Domain"
            Workgroup = Get-SafeProperty -Object $SystemInfo -PropertyName "Workgroup"
            AssessmentScope = "8 Critical Windows Server Security Controls"
            AuditUser = $env:USERNAME
            UserDomain = $env:USERDOMAIN
        }

        $AuditResults.Add("AuditMetadata", $AuditMetadata)
        Write-Host "[SUCCESS] Legacy audit metadata collected for compatibility" -ForegroundColor Green
    }
    catch {
        Write-Host "[ERROR] Failed to collect legacy audit metadata: $($_.Exception.Message)" -ForegroundColor Red
        $AuditResults.Add("AuditMetadata", @{ Error = $_.Exception.Message })
    }

    # =============================================================================
    # ENHANCED PRE-FLIGHT VALIDATION
    # =============================================================================

    Write-Host "Performing enhanced pre-flight validation..." -ForegroundColor Yellow

    $PreflightResults = Test-Prerequisites
    if ($PreflightResults.Warnings.Count -gt 0) {
        Write-Host "[WARNING] Pre-flight checks found issues:" -ForegroundColor Yellow
        foreach ($Warning in $PreflightResults.Warnings) {
            Write-Host "  - $Warning" -ForegroundColor Yellow
        }
    }

    # =============================================================================
    # PRE-FLIGHT VALIDATION
    # =============================================================================

    Write-Host "Performing pre-flight validation..." -ForegroundColor Yellow

    # Check if running as Administrator
    try {
        $CurrentPrincipal = New-Object Security.Principal.WindowsPrincipal([Security.Principal.WindowsIdentity]::GetCurrent())
        $IsAdmin = $CurrentPrincipal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)

        if ($IsAdmin) {
            Write-Host "[SUCCESS] Running with Administrator privileges" -ForegroundColor Green
        } else {
            Write-Host "[WARNING] Not running as Administrator - some checks may be limited" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "[ERROR] Cannot verify Administrator status: $($_.Exception.Message)" -ForegroundColor Red
    }

    # Check Windows Server version
    try {
        $OSVersionInfo = Get-SafeWmiObject -ClassName "Win32_OperatingSystem"
        $OSVersion = Get-SafeProperty -Object $OSVersionInfo -PropertyName "Caption"
        if ($OSVersion -like "*Server 2019*" -or $OSVersion -like "*Server 2016*" -or $OSVersion -like "*Server 2022*") {
            Write-Host "[SUCCESS] Windows Server version compatible: $OSVersion" -ForegroundColor Green
        } else {
            Write-Host "[WARNING] Unexpected OS version: $OSVersion" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "[ERROR] Cannot verify OS version: $($_.Exception.Message)" -ForegroundColor Red
    }

    # =============================================================================
    # CONTROL 1: WINDOWS UPDATE AND PATCH MANAGEMENT
    # =============================================================================

    Write-Host "Step 3/9: Assessing Windows Update and patch management..." -ForegroundColor Yellow

    try {
        # Get Windows Update service status
        $UpdateService = Get-Service -Name "wuauserv" -ErrorAction SilentlyContinue

        # Get recent updates (last 30 days)
        $RecentUpdates = Get-HotFix | Where-Object { $_.InstalledOn -gt (Get-Date).AddDays(-30) } | Sort-Object InstalledOn -Descending

        # Get Windows Update settings from registry
        $UpdateSettings = @{}
        try {
            $UpdateSettings.AutoUpdateEnabled = (Get-ItemProperty -Path "HKLM:\SOFTWARE\Policies\Microsoft\Windows\WindowsUpdate\AU" -Name "NoAutoUpdate" -ErrorAction SilentlyContinue).NoAutoUpdate -eq 0
            $UpdateSettings.AutoInstallMinorUpdates = (Get-ItemProperty -Path "HKLM:\SOFTWARE\Policies\Microsoft\Windows\WindowsUpdate\AU" -Name "AutoInstallMinorUpdates" -ErrorAction SilentlyContinue).AutoInstallMinorUpdates
        } catch {
            $UpdateSettings.RegistryAccessError = $_.Exception.Message
        }

        $WindowsUpdateControl = @{
            ControlID = "WIN-1.1"
            ControlName = "Windows Update and Patch Management"
            RiskLevel = "Critical"
            CVSSScore = 9.8
            UpdateServiceStatus = if ($UpdateService) { $UpdateService.Status } else { "Not Found" }
            UpdateServiceStartType = if ($UpdateService) { $UpdateService.StartType } else { "Unknown" }
            RecentUpdatesCount = $RecentUpdates.Count
            RecentUpdates = $RecentUpdates | Select-Object HotFixID, Description, InstalledBy, InstalledOn -First 10
            UpdateSettings = $UpdateSettings
            LastUpdateInstalled = if ($RecentUpdates) { $RecentUpdates[0].InstalledOn } else { "No recent updates found" }
            CurrentValue = "Service: $($UpdateService.Status), Recent updates: $($RecentUpdates.Count)"
            BaselineValue = "Windows Update service running, regular updates installed"
            ComplianceStatus = if ($UpdateService.Status -eq "Running" -and $RecentUpdates.Count -gt 0) { "Compliant" } else { "Review Required" }
            ComplianceScore = if ($UpdateService.Status -eq "Running" -and $RecentUpdates.Count -gt 0) { 100 } elseif ($UpdateService.Status -eq "Running") { 75 } else { 25 }
            Finding = if ($UpdateService.Status -eq "Running" -and $RecentUpdates.Count -gt 0) { "Windows Update service active with recent updates" } else { "Windows Update service or recent updates missing" }
            Recommendation = "Ensure Windows Update service is running and system receives regular security updates"
            AssessmentDate = $AuditStartTime
        }

        $AuditResults.Add("WIN_1_1_WindowsUpdate", $WindowsUpdateControl)
        Write-Host "[SUCCESS] Windows Update and patch management assessed" -ForegroundColor Green
    }
    catch {
        Write-Host "[ERROR] Failed to assess Windows Update: $($_.Exception.Message)" -ForegroundColor Red
        $AuditResults.Add("WIN_1_1_WindowsUpdate", @{ Error = $_.Exception.Message })
    }

    # =============================================================================
    # CONTROL 2: USER ACCOUNT AND PASSWORD POLICIES
    # =============================================================================

    Write-Host "Step 4/9: Assessing user account and password policies..." -ForegroundColor Yellow

    try {
        # Get local users
        $LocalUsers = Get-LocalUser -ErrorAction SilentlyContinue

        # Get password policy using safer approach
        $PasswordPolicy = Get-SafePasswordPolicy

        # Check for disabled default accounts
        $AdminAccount = $LocalUsers | Where-Object { $_.Name -eq "Administrator" }
        $GuestAccount = $LocalUsers | Where-Object { $_.Name -eq "Guest" }

        # Safe integer conversion for password length
        $MinPasswordLengthInt = 0
        if ($PasswordPolicy.MinPasswordLength -and [int]::TryParse($PasswordPolicy.MinPasswordLength, [ref]$MinPasswordLengthInt)) {
            $PasswordLengthCompliant = $MinPasswordLengthInt -ge $COMPLIANCE_THRESHOLDS.MinPasswordLength
        } else {
            $PasswordLengthCompliant = $false
            Write-Host "[WARNING] Could not parse minimum password length: $($PasswordPolicy.MinPasswordLength)" -ForegroundColor Yellow
        }

        $UserAccountControl = @{
            ControlID = "WIN-2.1"
            ControlName = "User Account and Password Policies"
            RiskLevel = "High"
            CVSSScore = 8.2
            TotalLocalUsers = $LocalUsers.Count
            LocalUsers = $LocalUsers | Select-Object Name, Enabled, LastLogon, PasswordRequired, PasswordExpires -First 20
            PasswordPolicy = $PasswordPolicy
            AdminAccountEnabled = if ($AdminAccount) { $AdminAccount.Enabled } else { $false }
            GuestAccountEnabled = if ($GuestAccount) { $GuestAccount.Enabled } else { $false }
            CurrentValue = "Min password length: $($PasswordPolicy.MinPasswordLength), Admin enabled: $($AdminAccount.Enabled), Guest enabled: $($GuestAccount.Enabled)"
            BaselineValue = "Strong password policy, Administrator disabled, Guest disabled"
            ComplianceStatus = if ($PasswordLengthCompliant -and -not $AdminAccount.Enabled -and -not $GuestAccount.Enabled) { "Compliant" } else { "Review Required" }
            ComplianceScore = if ($PasswordLengthCompliant -and -not $AdminAccount.Enabled -and -not $GuestAccount.Enabled) { 100 } else { 50 }
            Finding = if ($PasswordLengthCompliant) { "Password policy appears adequate" } else { "Weak password policy detected" }
            Recommendation = "Implement strong password policy, disable default Administrator and Guest accounts"
            AssessmentDate = $AuditStartTime
        }

        $AuditResults.Add("WIN_2_1_UserAccounts", $UserAccountControl)
        Write-Host "[SUCCESS] User account and password policies assessed" -ForegroundColor Green
    }
    catch {
        Write-Host "[ERROR] Failed to assess user accounts: $($_.Exception.Message)" -ForegroundColor Red
        $AuditResults.Add("WIN_2_1_UserAccounts", @{ Error = $_.Exception.Message })
    }

    # =============================================================================
    # CONTROL 3: WINDOWS FIREWALL CONFIGURATION
    # =============================================================================

    Write-Host "Step 5/9: Assessing Windows Firewall configuration..." -ForegroundColor Yellow

    try {
        # Get Windows Firewall profiles
        $FirewallProfiles = Get-NetFirewallProfile -ErrorAction Stop

        # Get Windows Firewall service status
        $FirewallService = Get-Service -Name "MpsSvc" -ErrorAction SilentlyContinue

        # Get firewall rules (sample)
        $FirewallRules = Get-NetFirewallRule | Where-Object { $_.Enabled -eq $true } | Select-Object DisplayName, Direction, Action, Profile -First 50

        $FirewallControl = @{
            ControlID = "WIN-3.1"
            ControlName = "Windows Firewall Configuration"
            RiskLevel = "High"
            CVSSScore = 7.8
            FirewallServiceStatus = if ($FirewallService) { $FirewallService.Status } else { "Not Found" }
            FirewallProfiles = $FirewallProfiles | Select-Object Name, Enabled, DefaultInboundAction, DefaultOutboundAction, LogFileName
            EnabledProfiles = ($FirewallProfiles | Where-Object { $_.Enabled }).Count
            TotalProfiles = $FirewallProfiles.Count
            ActiveRulesCount = $FirewallRules.Count
            SampleRules = $FirewallRules | Select-Object -First 20
            CurrentValue = "Service: $($FirewallService.Status), Enabled profiles: $($EnabledProfiles)/$($FirewallProfiles.Count)"
            BaselineValue = "Windows Firewall service running, all profiles enabled"
            ComplianceStatus = if ($FirewallService.Status -eq "Running" -and $EnabledProfiles -eq $FirewallProfiles.Count) { "Compliant" } else { "Review Required" }
            ComplianceScore = if ($FirewallService.Status -eq "Running" -and $EnabledProfiles -eq $FirewallProfiles.Count) { 100 } elseif ($FirewallService.Status -eq "Running") { 75 } else { 25 }
            Finding = if ($FirewallService.Status -eq "Running" -and $EnabledProfiles -eq $FirewallProfiles.Count) { "Windows Firewall properly configured and enabled" } else { "Windows Firewall configuration issues detected" }
            Recommendation = "Ensure Windows Firewall service is running and all profiles are enabled with appropriate rules"
            AssessmentDate = $AuditStartTime
        }

        $AuditResults.Add("WIN_3_1_WindowsFirewall", $FirewallControl)
        Write-Host "[SUCCESS] Windows Firewall configuration assessed" -ForegroundColor Green
    }
    catch {
        Write-Host "[ERROR] Failed to assess Windows Firewall: $($_.Exception.Message)" -ForegroundColor Red
        $AuditResults.Add("WIN_3_1_WindowsFirewall", @{ Error = $_.Exception.Message })
    }

    # =============================================================================
    # CONTROL 4: AUDIT LOGGING AND EVENT LOG SETTINGS
    # =============================================================================

    Write-Host "Step 6/9: Assessing audit logging and event log settings..." -ForegroundColor Yellow

    try {
        # Get Event Log service status
        $EventLogService = Get-Service -Name "EventLog" -ErrorAction SilentlyContinue

        # Get critical event logs
        $EventLogs = @("System", "Security", "Application")
        $EventLogInfo = [System.Collections.ArrayList]@()

        foreach ($LogName in $EventLogs) {
            try {
                $Log = Get-WinEvent -ListLog $LogName -ErrorAction SilentlyContinue
                if ($Log) {
                    [void]$EventLogInfo.Add(@{
                        LogName = $Log.LogName
                        IsEnabled = $Log.IsEnabled
                        MaximumSizeInBytes = $Log.MaximumSizeInBytes
                        RecordCount = $Log.RecordCount
                        LogMode = $Log.LogMode
                    })
                }
            } catch {
                [void]$EventLogInfo.Add(@{
                    LogName = $LogName
                    Error = $_.Exception.Message
                })
            }
        }

        # Check audit policy settings using safer approach
        $AuditPolicy = Get-SafeAuditPolicy

        $AuditLoggingControl = @{
            ControlID = "WIN-4.1"
            ControlName = "Audit Logging and Event Log Settings"
            RiskLevel = "Medium"
            CVSSScore = 6.5
            EventLogServiceStatus = if ($EventLogService) { $EventLogService.Status } else { "Not Found" }
            EventLogs = $EventLogInfo
            EnabledEventLogs = ($EventLogInfo | Where-Object { $_.IsEnabled }).Count
            TotalEventLogs = $EventLogInfo.Count
            AuditPolicy = $AuditPolicy
            CurrentValue = "Event Log service: $($EventLogService.Status), Enabled logs: $($EnabledEventLogs)/$($EventLogInfo.Count)"
            BaselineValue = "Event Log service running, critical logs enabled with adequate retention"
            ComplianceStatus = if ($EventLogService.Status -eq "Running" -and $EnabledEventLogs -eq $EventLogInfo.Count) { "Compliant" } else { "Review Required" }
            ComplianceScore = if ($EventLogService.Status -eq "Running" -and $EnabledEventLogs -eq $EventLogInfo.Count) { 100 } elseif ($EventLogService.Status -eq "Running") { 75 } else { 25 }
            Finding = if ($EventLogService.Status -eq "Running") { "Event logging service active" } else { "Event logging service issues detected" }
            Recommendation = "Ensure Event Log service is running and critical event logs are enabled with appropriate retention policies"
            AssessmentDate = $AuditStartTime
        }

        $AuditResults.Add("WIN_4_1_AuditLogging", $AuditLoggingControl)
        Write-Host "[SUCCESS] Audit logging and event log settings assessed" -ForegroundColor Green
    }
    catch {
        Write-Host "[ERROR] Failed to assess audit logging: $($_.Exception.Message)" -ForegroundColor Red
        $AuditResults.Add("WIN_4_1_AuditLogging", @{ Error = $_.Exception.Message })
    }

    # =============================================================================
    # CONTROL 5: SERVICE CONFIGURATIONS AND SECURITY
    # =============================================================================

    Write-Host "Step 7/9: Assessing service configurations and security..." -ForegroundColor Yellow

    try {
        # Get all services
        $AllServices = Get-Service -ErrorAction Stop

        # Focus on critical security services
        $CriticalServices = [System.Collections.ArrayList]@()
        $SecurityServiceNames = @("wuauserv", "MpsSvc", "EventLog", "Winmgmt", "RpcSs", "BITS", "CryptSvc", "TrustedInstaller")

        foreach ($ServiceName in $SecurityServiceNames) {
            $Service = $AllServices | Where-Object { $_.Name -eq $ServiceName }
            if ($Service) {
                [void]$CriticalServices.Add(@{
                    Name = $Service.Name
                    DisplayName = $Service.DisplayName
                    Status = $Service.Status
                    StartType = $Service.StartType
                })
            }
        }

        # Get services running as system accounts using safer WMI approach
        $SystemServicesWmi = Get-SafeWmiObject -ClassName "Win32_Service"
        $SystemServices = if ($SystemServicesWmi) {
            $SystemServicesWmi | Where-Object { $_.StartName -like "*System*" -or $_.StartName -like "*LocalService*" -or $_.StartName -like "*NetworkService*" } | Select-Object Name, StartName, State -First 20
        } else {
            @()
        }

        # Count running services
        $RunningServices = ($AllServices | Where-Object { $_.Status -eq "Running" }).Count
        $StoppedServices = ($AllServices | Where-Object { $_.Status -eq "Stopped" }).Count

        $ServiceControl = @{
            ControlID = "WIN-5.1"
            ControlName = "Service Configurations and Security"
            RiskLevel = "Medium"
            CVSSScore = 6.8
            TotalServices = $AllServices.Count
            RunningServices = $RunningServices
            StoppedServices = $StoppedServices
            CriticalServices = $CriticalServices
            CriticalServicesRunning = ($CriticalServices | Where-Object { $_.Status -eq "Running" }).Count
            SystemServices = $SystemServices
            CurrentValue = "Total services: $($AllServices.Count), Running: $RunningServices, Critical running: $($CriticalServices | Where-Object { $_.Status -eq 'Running' } | Measure-Object).Count"
            BaselineValue = "Critical security services running, unnecessary services disabled"
            ComplianceStatus = if (($CriticalServices | Where-Object { $_.Status -eq "Running" }).Count -ge ($CriticalServices.Count * $COMPLIANCE_THRESHOLDS.CriticalServices)) { "Compliant" } else { "Review Required" }
            ComplianceScore = if ($CriticalServices.Count -gt 0) {
                $RunningCriticalServices = ($CriticalServices | Where-Object { $_.Status -eq "Running" }).Count
                [math]::Round(($RunningCriticalServices / $CriticalServices.Count) * 100, 0)
            } else {
                0
            }
            Finding = if (($CriticalServices | Where-Object { $_.Status -eq "Running" }).Count -ge ($CriticalServices.Count * $COMPLIANCE_THRESHOLDS.CriticalServices)) { "Critical security services appear to be running" } else { "Some critical security services are not running" }
            Recommendation = "Ensure critical security services are running and unnecessary services are disabled"
            AssessmentDate = $AuditStartTime
        }

        $AuditResults.Add("WIN_5_1_ServiceSecurity", $ServiceControl)
        Write-Host "[SUCCESS] Service configurations and security assessed" -ForegroundColor Green
    }
    catch {
        Write-Host "[ERROR] Failed to assess service security: $($_.Exception.Message)" -ForegroundColor Red
        $AuditResults.Add("WIN_5_1_ServiceSecurity", @{ Error = $_.Exception.Message })
    }

    # =============================================================================
    # CONTROL 6: REGISTRY SECURITY SETTINGS
    # =============================================================================

    Write-Host "Step 8/9: Assessing registry security settings..." -ForegroundColor Yellow

    try {
        # Check critical registry security settings
        $RegistrySettings = [System.Collections.ArrayList]@()

        # Security-related registry keys to check
        $SecurityKeys = @(
            @{ Path = "HKLM:\SYSTEM\CurrentControlSet\Control\Lsa"; Name = "LmCompatibilityLevel"; Description = "LM Authentication Level" },
            @{ Path = "HKLM:\SYSTEM\CurrentControlSet\Control\Lsa"; Name = "NoLMHash"; Description = "Disable LM Hash Storage" },
            @{ Path = "HKLM:\SYSTEM\CurrentControlSet\Services\Netlogon\Parameters"; Name = "RequireSignOrSeal"; Description = "Netlogon Signing Required" },
            @{ Path = "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System"; Name = "EnableLUA"; Description = "User Account Control Enabled" },
            @{ Path = "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System"; Name = "ConsentPromptBehaviorAdmin"; Description = "UAC Admin Consent Behavior" }
        )

        foreach ($Key in $SecurityKeys) {
            try {
                $Value = Get-ItemProperty -Path $Key.Path -Name $Key.Name -ErrorAction SilentlyContinue
                [void]$RegistrySettings.Add(@{
                    Path = $Key.Path
                    Name = $Key.Name
                    Description = $Key.Description
                    Value = if ($Value) { $Value.($Key.Name) } else { "Not Found" }
                    Status = if ($Value) { "Found" } else { "Missing" }
                })
            } catch {
                [void]$RegistrySettings.Add(@{
                    Path = $Key.Path
                    Name = $Key.Name
                    Description = $Key.Description
                    Value = "Error"
                    Status = "Access Denied"
                    Error = $_.Exception.Message
                })
            }
        }

        # Count secure settings
        $SecureSettings = ($RegistrySettings | Where-Object { $_.Status -eq "Found" }).Count

        $RegistryControl = @{
            ControlID = "WIN-6.1"
            ControlName = "Registry Security Settings"
            RiskLevel = "Medium"
            CVSSScore = 6.2
            TotalSecurityKeys = $SecurityKeys.Count
            FoundSecurityKeys = $SecureSettings
            RegistrySettings = $RegistrySettings
            CurrentValue = "Security registry keys found: $SecureSettings/$($SecurityKeys.Count)"
            BaselineValue = "All critical security registry settings properly configured"
            ComplianceStatus = if ($SecureSettings -ge ($SecurityKeys.Count * $COMPLIANCE_THRESHOLDS.RegistrySettings)) { "Compliant" } else { "Review Required" }
            ComplianceScore = if ($SecurityKeys.Count -gt 0) { [math]::Round(($SecureSettings / $SecurityKeys.Count) * 100, 0) } else { 0 }
            Finding = if ($SecureSettings -ge ($SecurityKeys.Count * $COMPLIANCE_THRESHOLDS.RegistrySettings)) { "Most critical registry security settings are present" } else { "Some critical registry security settings are missing" }
            Recommendation = "Review and configure critical security registry settings according to security baselines"
            AssessmentDate = $AuditStartTime
        }

        $AuditResults.Add("WIN_6_1_RegistrySecurity", $RegistryControl)
        Write-Host "[SUCCESS] Registry security settings assessed" -ForegroundColor Green
    }
    catch {
        Write-Host "[ERROR] Failed to assess registry security: $($_.Exception.Message)" -ForegroundColor Red
        $AuditResults.Add("WIN_6_1_RegistrySecurity", @{ Error = $_.Exception.Message })
    }

    # =============================================================================
    # CONTROL 7: FILE SYSTEM PERMISSIONS ON CRITICAL DIRECTORIES
    # =============================================================================

    Write-Host "Step 9/9: Assessing file system permissions on critical directories..." -ForegroundColor Yellow

    try {
        $DirectoryPermissions = [System.Collections.ArrayList]@()

        foreach ($Directory in $CRITICAL_DIRECTORIES) {
            try {
                if (Test-Path $Directory) {
                    $Acl = Get-Acl $Directory -ErrorAction SilentlyContinue
                    $OwnerInfo = if ($Acl) { $Acl.Owner } else { "Unknown" }
                    $AccessRules = if ($Acl) { $Acl.Access | Select-Object IdentityReference, FileSystemRights, AccessControlType -First 10 } else { @() }

                    [void]$DirectoryPermissions.Add(@{
                        Path = $Directory
                        Exists = $true
                        Owner = $OwnerInfo
                        AccessRulesCount = $AccessRules.Count
                        SampleAccessRules = $AccessRules
                    })
                } else {
                    [void]$DirectoryPermissions.Add(@{
                        Path = $Directory
                        Exists = $false
                        Owner = "N/A"
                        AccessRulesCount = 0
                        SampleAccessRules = @()
                    })
                }
            } catch {
                [void]$DirectoryPermissions.Add(@{
                    Path = $Directory
                    Exists = "Unknown"
                    Owner = "Error"
                    Error = $_.Exception.Message
                })
            }
        }

        $ExistingDirectories = ($DirectoryPermissions | Where-Object { $_.Exists -eq $true }).Count

        $FileSystemControl = @{
            ControlID = "WIN-7.1"
            ControlName = "File System Permissions on Critical Directories"
            RiskLevel = "Medium"
            CVSSScore = 5.8
            TotalCriticalDirectories = $CRITICAL_DIRECTORIES.Count
            ExistingDirectories = $ExistingDirectories
            DirectoryPermissions = $DirectoryPermissions
            CurrentValue = "Critical directories found: $ExistingDirectories/$($CRITICAL_DIRECTORIES.Count)"
            BaselineValue = "All critical directories exist with proper permissions"
            ComplianceStatus = if ($ExistingDirectories -eq $CRITICAL_DIRECTORIES.Count) { "Compliant" } else { "Review Required" }
            ComplianceScore = if ($CRITICAL_DIRECTORIES.Count -gt 0) { [math]::Round(($ExistingDirectories / $CRITICAL_DIRECTORIES.Count) * 100, 0) } else { 100 }
            Finding = if ($ExistingDirectories -eq $CRITICAL_DIRECTORIES.Count) { "All critical directories exist and are accessible" } else { "Some critical directories are missing or inaccessible" }
            Recommendation = "Verify critical system directories exist with appropriate permissions and ownership"
            AssessmentDate = $AuditStartTime
        }

        $AuditResults.Add("WIN_7_1_FileSystemSecurity", $FileSystemControl)
        Write-Host "[SUCCESS] File system permissions assessed" -ForegroundColor Green
    }
    catch {
        Write-Host "[ERROR] Failed to assess file system permissions: $($_.Exception.Message)" -ForegroundColor Red
        $AuditResults.Add("WIN_7_1_FileSystemSecurity", @{ Error = $_.Exception.Message })
    }

    # =============================================================================
    # COMPLIANCE SUMMARY AND FINAL PROCESSING
    # =============================================================================

    Write-Host ""
    Write-Host "Generating compliance summary..." -ForegroundColor Yellow

    $AuditEndTime = Get-Date
    $AuditDuration = $AuditEndTime - $AuditStartTime

    # Calculate overall compliance metrics
    $TotalControls = 7
    $SuccessfulAssessments = ($AuditResults.Keys | Where-Object {
        $_ -ne "AuditMetadata" -and $_ -ne "ComplianceSummary" -and
        (-not $AuditResults[$_].ContainsKey("Error") -or $AuditResults[$_].Error -eq $null)
    }).Count
    $FailedAssessments = $TotalControls - $SuccessfulAssessments

    $ComplianceSummary = @{
        AssessmentType = "Windows Server 2019 Security Controls Audit"
        TotalControlsAssessed = $TotalControls
        SuccessfulAssessments = $SuccessfulAssessments
        FailedAssessments = $FailedAssessments
        AuditStartTime = $AuditStartTime
        AuditEndTime = $AuditEndTime
        AuditDurationSeconds = [math]::Round($AuditDuration.TotalSeconds, 2)
        AuditID = $AuditID
        FrameworkVersion = "Windows Server Security Controls v1.0"
        AuditType = "Read-Only Assessment - Production Safe"
        AuditExecutedBy = $env:USERNAME
        ComputerName = $env:COMPUTERNAME
        PowerShellVersion = $PSVersionTable.PSVersion.ToString()
    }

    $AuditResults.Add("ComplianceSummary", $ComplianceSummary)

    # =============================================================================
    # GENERATE AUDIT INTEGRITY HASHES AND TAMPER DETECTION
    # =============================================================================

    Write-Host ""
    Write-Host "Generating audit integrity hashes for tamper detection..." -ForegroundColor Yellow

    try {
        # Generate comprehensive integrity hashes for the entire audit results
        $AuditIntegrityHashes = New-SystemIntegrityHash -SystemData $AuditResults

        # Add audit signature and validation metadata
        $AuditSignature = @{
            AuditIntegrityHashes = $AuditIntegrityHashes
            SignatureTimestamp = Get-Date -Format "yyyy-MM-ddTHH:mm:ss.fffZ"
            SignedBy = [System.Security.Principal.WindowsIdentity]::GetCurrent().Name
            AuditVersion = "Windows Server Security Audit v2.0 with Hardware Fingerprinting"
            IntegrityAlgorithms = @("SHA256", "SHA512", "MD5", "CRC32")
            ValidationInstructions = @{
                Usage = "Use Test-SystemIntegrity function to validate audit results integrity"
                Example = 'Test-SystemIntegrity -CurrentSystemData $AuditResults -StoredHashes $AuditResults.AuditSignature.AuditIntegrityHashes'
                Warning = "Any modification to this audit file will be detected through hash validation"
                HardwareVerification = "Compare SystemIdentity.CompositeHardwareFingerprint to verify same physical machine"
            }
        }

        $AuditResults.Add("AuditSignature", $AuditSignature)
        Write-Host "[SUCCESS] Audit integrity hashes generated for tamper detection" -ForegroundColor Green
        Write-Host "          SHA256: $($AuditIntegrityHashes.SHA256.Substring(0,32))..." -ForegroundColor Cyan
        Write-Host "          Data Length: $($AuditIntegrityHashes.DataLength) bytes" -ForegroundColor Cyan
    }
    catch {
        Write-Host "[ERROR] Failed to generate audit integrity hashes: $($_.Exception.Message)" -ForegroundColor Red
        $AuditResults.Add("AuditSignature", @{ Error = $_.Exception.Message })
    }

} # End of main try block
catch {
    Write-Host "[ERROR] Critical error during audit execution: $($_.Exception.Message)" -ForegroundColor Red
    $AuditResults.Add("CriticalError", @{
        Message = $_.Exception.Message
        StackTrace = $_.Exception.StackTrace
        Timestamp = Get-Date
    })
} finally {
    # =============================================================================
    # OUTPUT GENERATION AND COMPLETION
    # =============================================================================

    Write-Host ""
    Write-Host "Generating JSON output..." -ForegroundColor Yellow

    try {
        # Apply obfuscation to protect audit methodology
        Write-Host "Applying field obfuscation to protect audit methodology..." -ForegroundColor Yellow
        $ObfuscatedResults = ConvertTo-ObfuscatedHashtable -InputHashtable $AuditResults

        # Convert obfuscated results to JSON
        $JsonOutput = $ObfuscatedResults | ConvertTo-Json -Depth 10 -ErrorAction Stop

        # Handle encryption if requested
        if ($EncryptOutput) {
            Write-Host "Encrypting audit results..." -ForegroundColor Yellow

            # Get encryption password
            if (-not $EncryptionPassword) {
                $SecurePassword = Read-Host "Enter encryption password" -AsSecureString
                $EncryptionPassword = [System.Runtime.InteropServices.Marshal]::PtrToStringAuto([System.Runtime.InteropServices.Marshal]::SecureStringToBSTR($SecurePassword))
            }

            # Encrypt the JSON data
            $EncryptedPackage = Protect-JsonData -JsonData $JsonOutput -Password $EncryptionPassword -Algorithm $EncryptionAlgorithm

            # Save encrypted package
            $EncryptedJson = $EncryptedPackage | ConvertTo-Json -Depth 5
            $EncryptedOutputPath = $OutputPath -replace '\.json$', '.encrypted.json'
            $EncryptedJson | Out-File -FilePath $EncryptedOutputPath -Encoding UTF8 -ErrorAction Stop

            Write-Host "[SUCCESS] Encrypted audit results saved to: $EncryptedOutputPath" -ForegroundColor Green
            Write-Host "          Algorithm: $EncryptionAlgorithm" -ForegroundColor Cyan
            Write-Host "          Original Size: $($EncryptedPackage.DataLength) bytes" -ForegroundColor Cyan
            Write-Host "          Encrypted Size: $($EncryptedPackage.EncryptedLength) bytes" -ForegroundColor Cyan

            # Also save unencrypted version if requested (for testing)
            if ($PSBoundParameters.ContainsKey('OutputPath')) {
                $JsonOutput | Out-File -FilePath $OutputPath -Encoding UTF8 -ErrorAction Stop
                Write-Host "[INFO] Unencrypted version also saved to: $OutputPath" -ForegroundColor Yellow
            }
        } else {
            # Save unencrypted JSON
            $JsonOutput | Out-File -FilePath $OutputPath -Encoding UTF8 -ErrorAction Stop
        }

        Write-Host ""
        Write-Host "=================================================================================" -ForegroundColor Green
        Write-Host "Windows Server 2019 Enhanced Security Audit Completed" -ForegroundColor Green
        Write-Host "=================================================================================" -ForegroundColor Green
        Write-Host "Audit ID: $AuditID" -ForegroundColor Cyan
        Write-Host "End Time: $((Get-Date).ToString('yyyy-MM-dd HH:mm:ss'))" -ForegroundColor Cyan
        Write-Host "Duration: $([math]::Round(((Get-Date) - $AuditStartTime).TotalSeconds, 2)) seconds" -ForegroundColor Cyan
        Write-Host "Controls Assessed: 7" -ForegroundColor Cyan
        Write-Host "Output File: $OutputPath" -ForegroundColor Cyan
        Write-Host ""
        Write-Host "[ENHANCED FEATURES]:" -ForegroundColor Yellow
        if ($AuditResults.ContainsKey("SystemIdentity") -and $AuditResults.SystemIdentity.CompositeHardwareFingerprint) {
            Write-Host "   Hardware Fingerprint: $($AuditResults.SystemIdentity.CompositeHardwareFingerprint.Substring(0,32))..." -ForegroundColor Cyan
            Write-Host "   CPU ID: $($AuditResults.SystemIdentity.HardwareFingerprint.CPU.ProcessorId)" -ForegroundColor Cyan
            Write-Host "   Motherboard Serial: $($AuditResults.SystemIdentity.HardwareFingerprint.Motherboard.BaseBoardSerialNumber)" -ForegroundColor Cyan
        }
        if ($AuditResults.ContainsKey("AuditSignature") -and $AuditResults.AuditSignature.AuditIntegrityHashes) {
            Write-Host "   Tamper Detection: SHA256 $($AuditResults.AuditSignature.AuditIntegrityHashes.SHA256.Substring(0,16))..." -ForegroundColor Cyan
            Write-Host "   Data Integrity: $($AuditResults.AuditSignature.AuditIntegrityHashes.DataLength) bytes protected" -ForegroundColor Cyan
        }
        Write-Host "   Field Obfuscation: Applied to protect audit methodology" -ForegroundColor Cyan
        Write-Host ""
        if ($EncryptOutput) {
            $FinalOutputPath = $OutputPath -replace '\.json$', '.encrypted.json'
            Write-Host "[SUCCESS] Enhanced JSON audit results with hardware fingerprinting and encryption saved" -ForegroundColor Green
            Write-Host "[SUCCESS] Tamper detection enabled - any file modification will be detected" -ForegroundColor Green
            Write-Host "[SUCCESS] Encryption applied using $EncryptionAlgorithm algorithm" -ForegroundColor Green
            Write-Host "[SUCCESS] Send the encrypted file '$FinalOutputPath' for compliance analysis" -ForegroundColor Green
        } else {
            Write-Host "[SUCCESS] Enhanced JSON audit results with hardware fingerprinting saved" -ForegroundColor Green
            Write-Host "[SUCCESS] Tamper detection enabled - any file modification will be detected" -ForegroundColor Green
            Write-Host "[SUCCESS] Send the file '$OutputPath' for compliance analysis" -ForegroundColor Green
        }
        Write-Host "=================================================================================" -ForegroundColor Green

        # Display summary statistics
        if ($AuditResults.ContainsKey("ComplianceSummary")) {
            $Summary = $AuditResults["ComplianceSummary"]
            Write-Host ""
            Write-Host "AUDIT SUMMARY:" -ForegroundColor Yellow
            Write-Host "- Total Controls: $($Summary.TotalControlsAssessed)" -ForegroundColor White
            Write-Host "- Successful Assessments: $($Summary.SuccessfulAssessments)" -ForegroundColor Green
            Write-Host "- Failed Assessments: $($Summary.FailedAssessments)" -ForegroundColor Red
            Write-Host "- Success Rate: $([math]::Round(($Summary.SuccessfulAssessments / $Summary.TotalControlsAssessed) * 100, 1))%" -ForegroundColor Cyan
        }

    } catch {
        Write-Host "[ERROR] Failed to save JSON output: $($_.Exception.Message)" -ForegroundColor Red

        # Provide specific guidance based on common error types
        if ($_.Exception.Message -like "*Access*denied*" -or $_.Exception.Message -like "*UnauthorizedAccessException*") {
            Write-Host "[GUIDANCE] Access denied - try running as Administrator or choose a different output path" -ForegroundColor Yellow
        } elseif ($_.Exception.Message -like "*DirectoryNotFoundException*" -or $_.Exception.Message -like "*path*not*found*") {
            Write-Host "[GUIDANCE] Directory not found - ensure the output directory exists" -ForegroundColor Yellow
        } elseif ($_.Exception.Message -like "*disk*full*" -or $_.Exception.Message -like "*insufficient*space*") {
            Write-Host "[GUIDANCE] Insufficient disk space - free up space or choose a different location" -ForegroundColor Yellow
        }

        Write-Host "Attempting to save to alternative location..." -ForegroundColor Yellow

        try {
            $AlternativePath = ".\Windows-Server-2019-Security-Results-$(Get-Date -Format 'yyyyMMdd-HHmmss').json"
            # Ensure obfuscated results are used for alternative path as well
            if (-not $JsonOutput) {
                $ObfuscatedResults = ConvertTo-ObfuscatedHashtable -InputHashtable $AuditResults
                $JsonOutput = $ObfuscatedResults | ConvertTo-Json -Depth 10
            }
            $JsonOutput | Out-File -FilePath $AlternativePath -Encoding UTF8 -ErrorAction Stop
            Write-Host "[SUCCESS] Obfuscated JSON output saved to alternative location: $AlternativePath" -ForegroundColor Green
        } catch {
            Write-Host "[ERROR] Failed to save to alternative location. Displaying obfuscated results:" -ForegroundColor Red
            Write-Host $JsonOutput
        }
    }
}

<#
================================================================================
EXECUTION INSTRUCTIONS FOR ADMIN (Windows Server 2019 Enhanced):
================================================================================

1. WINDOWS SERVER LOCAL EXECUTION (Recommended):
   .\Windows-Server-2019-Security-Audit.ps1

2. WITH CUSTOM OUTPUT PATH:
   .\Windows-Server-2019-Security-Audit.ps1 -OutputPath "C:\Temp\Windows-Security-Results.json"

3. WITH ENCRYPTION (Recommended for sensitive environments):
   .\Windows-Server-2019-Security-Audit.ps1 -EncryptOutput
   .\Windows-Server-2019-Security-Audit.ps1 -EncryptOutput -EncryptionAlgorithm "AES256"
   .\Windows-Server-2019-Security-Audit.ps1 -EncryptOutput -EncryptionPassword "SecurePassword123!"

4. REMOTE EXECUTION (PowerShell Remoting):
   # Connect to remote server first, then run the script
   $Session = New-PSSession -ComputerName "ServerName" -Credential (Get-Credential)
   Invoke-Command -Session $Session -FilePath ".\Windows-Server-2019-Security-Audit.ps1"

PREREQUISITES:
- Windows Server 2019, 2016, or 2022
- PowerShell 5.1 or later
- Administrator privileges (recommended for complete assessment)
- Local or remote access to target Windows Server

ENHANCED FEATURES (NEW):
================================================================================
HARDWARE-LEVEL SYSTEM IDENTIFICATION:
- CPU serial number and signature extraction
- Motherboard and BIOS unique identifiers
- Memory module serial numbers and specifications
- Storage device signatures and firmware versions
- Network adapter hardware identifiers
- Composite hardware fingerprint (SHA256 hash)

TAMPER DETECTION AND INTEGRITY VALIDATION:
- Multi-layer checksums (SHA256, SHA512, MD5, CRC32)
- Automatic integrity hash generation
- Built-in tamper detection functions
- Cryptographic audit result signatures
- File modification detection capabilities

AUDIT METHODOLOGY PROTECTION:
- Field name obfuscation to protect audit techniques
- Hardware fingerprinting method concealment
- Tamper detection mechanism obfuscation
- Audit methodology security through obscurity
- Translation dictionary for internal analysis (audit-field-mapping.json)

JSON ENCRYPTION CAPABILITIES:
- AES-256, AES-128, and TripleDES encryption algorithms
- Password-based key derivation (PBKDF2 with 10,000 iterations)
- Secure random IV generation for each encryption
- Encrypted package with metadata for decryption
- Optional encryption for sensitive environments

MULTI-ENVIRONMENT AUTHENTICATION:
- Definitive physical machine identification
- Hardware-based system verification
- Environment-specific validation
- Audit trail integrity protection
- Cross-environment server disambiguation

IMPORTANT NOTES:
- This script is 100% READ-ONLY and safe for production
- No Windows configuration modifications are performed
- Enhanced output includes hardware fingerprinting and tamper detection
- Script assesses 7 critical Windows Server security controls:
  1. Windows Update and Patch Management
  2. User Account and Password Policies
  3. Windows Firewall Configuration
  4. Audit Logging and Event Log Settings
  5. Service Configurations and Security
  6. Registry Security Settings
  7. File System Permissions on Critical Directories
- Compatible with Windows Server 2016, 2019, and 2022
- All operations use only Get-* cmdlets, CIM queries, and read-only registry operations
- NO ADDITIONAL POWERSHELL MODULES REQUIRED - Uses only built-in cmdlets and .NET classes

SEND BACK: The generated Windows-Server-2019-Security-Results.json file

ENHANCED SECURITY VERIFICATION:
- Only Get-* and Get-CimInstance cmdlets used
- Only read-only WMI/CIM operations
- Only registry read operations (Get-ItemProperty)
- Hardware identification using built-in .NET cryptography classes
- No Set-*, New-*, Remove-*, or Enable-* cmdlets
- No configuration changes or system modifications
- Read-only access to Windows configuration and security settings
- Safe for production environments
- Tamper-evident audit results with cryptographic signatures

ENHANCED OUTPUT STRUCTURE:
The JSON output now includes:
- SystemIdentity: Complete hardware fingerprint and system identification
- AuditSignature: Integrity hashes and tamper detection data
- ValidationInstructions: Built-in integrity verification guidance
- Legacy AuditMetadata: Backward compatibility with existing tools

INTEGRITY VALIDATION EXAMPLE:
# To validate audit file integrity after receiving it:
$AuditData = Get-Content "Windows-Server-2019-Security-Results.json" | ConvertFrom-Json
$ValidationResult = Test-SystemIntegrity -CurrentSystemData $AuditData -StoredHashes $AuditData.AuditSignature.AuditIntegrityHashes
if ($ValidationResult.IntegrityVerified) {
    Write-Host "[SUCCESS] Audit file integrity verified - no tampering detected" -ForegroundColor Green
} else {
    Write-Host "[WARNING] Audit file has been modified!" -ForegroundColor Yellow
}

} # End of finally block

<#
ASSESSMENT FOCUS:
This enhanced script provides definitive proof of which physical machine was audited
and enables detection of any post-audit file tampering, addressing multi-environment
authentication concerns while maintaining focus on critical Windows Server security
configurations that directly impact system security posture.

================================================================================
#>
