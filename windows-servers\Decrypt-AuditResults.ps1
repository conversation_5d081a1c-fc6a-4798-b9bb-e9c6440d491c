#Requires -Version 5.1
<#
================================================================================
Audit Results Decryption Script
================================================================================
Description: Decrypts encrypted Windows Server security audit results
Version: 1.0
Created: January 9, 2025

USAGE:
1. .\Decrypt-AuditResults.ps1 -EncryptedFilePath "audit-results.encrypted.json"
2. .\Decrypt-AuditResults.ps1 -EncryptedFilePath "audit-results.encrypted.json" -OutputPath "decrypted-results.json"
3. .\Decrypt-AuditResults.ps1 -EncryptedFilePath "audit-results.encrypted.json" -Password "SecurePassword123!"

IMPORTANT: This script is for authorized audit analysis only
================================================================================
#>

[CmdletBinding()]
param(
    [Parameter(Mandatory = $true)]
    [string]$EncryptedFilePath,
    
    [Parameter(Mandatory = $false)]
    [string]$OutputPath = $null,
    
    [Parameter(Mandatory = $false)]
    [string]$Password = $null,
    
    [Parameter(Mandatory = $false)]
    [switch]$DeobfuscateFields
)

Write-Host "=================================================================================" -ForegroundColor Green
Write-Host "Audit Results Decryption Utility" -ForegroundColor Green
Write-Host "=================================================================================" -ForegroundColor Green
Write-Host "Encrypted File: $EncryptedFilePath" -ForegroundColor Cyan
Write-Host "=================================================================================" -ForegroundColor Green

# Load encrypted audit results
try {
    Write-Host "Loading encrypted audit results..." -ForegroundColor Yellow
    $EncryptedData = Get-Content -Path $EncryptedFilePath -Raw | ConvertFrom-Json -ErrorAction Stop
    Write-Host "[SUCCESS] Encrypted audit package loaded" -ForegroundColor Green
    Write-Host "          Algorithm: $($EncryptedData.Algorithm)" -ForegroundColor Cyan
    Write-Host "          Encrypted Size: $($EncryptedData.EncryptedLength) bytes" -ForegroundColor Cyan
    Write-Host "          Original Size: $($EncryptedData.DataLength) bytes" -ForegroundColor Cyan
    Write-Host "          Encrypted By: $($EncryptedData.EncryptedBy)" -ForegroundColor Cyan
    Write-Host "          Timestamp: $($EncryptedData.EncryptionTimestamp)" -ForegroundColor Cyan
} catch {
    Write-Host "[ERROR] Failed to load encrypted audit results: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Get decryption password
if (-not $Password) {
    $SecurePassword = Read-Host "Enter decryption password" -AsSecureString
    $Password = [System.Runtime.InteropServices.Marshal]::PtrToStringAuto([System.Runtime.InteropServices.Marshal]::SecureStringToBSTR($SecurePassword))
}

# Decryption function (copied from main audit script)
function Unprotect-JsonData {
    param(
        [PSCustomObject]$EncryptedPackage,
        [string]$Password
    )
    
    try {
        # Recreate encryption key using stored salt
        $Salt = [System.Convert]::FromBase64String($EncryptedPackage.Salt)
        $KeySize = switch ($EncryptedPackage.Algorithm) {
            "AES256" { 32 }
            "AES128" { 16 }
            "TripleDES" { 24 }
            default { 32 }
        }
        
        $PBKDF2 = New-Object System.Security.Cryptography.Rfc2898DeriveBytes($Password, $Salt, $EncryptedPackage.KeyDerivationIterations)
        $Key = $PBKDF2.GetBytes($KeySize)
        $PBKDF2.Dispose()
        
        # Create cipher for decryption
        $Cipher = switch ($EncryptedPackage.Algorithm) {
            "AES256" { [System.Security.Cryptography.Aes]::Create() }
            "AES128" { [System.Security.Cryptography.Aes]::Create() }
            "TripleDES" { [System.Security.Cryptography.TripleDES]::Create() }
            default { [System.Security.Cryptography.Aes]::Create() }
        }
        
        $Cipher.Key = $Key
        $Cipher.IV = [System.Convert]::FromBase64String($EncryptedPackage.IV)
        
        # Decrypt the data
        $EncryptedBytes = [System.Convert]::FromBase64String($EncryptedPackage.EncryptedData)
        $Decryptor = $Cipher.CreateDecryptor()
        
        $MemoryStream = New-Object System.IO.MemoryStream($EncryptedBytes)
        $CryptoStream = New-Object System.Security.Cryptography.CryptoStream($MemoryStream, $Decryptor, [System.Security.Cryptography.CryptoStreamMode]::Read)
        
        $DecryptedBytes = New-Object byte[] $EncryptedPackage.DataLength
        $CryptoStream.Read($DecryptedBytes, 0, $DecryptedBytes.Length)
        
        $DecryptedJson = [System.Text.Encoding]::UTF8.GetString($DecryptedBytes)
        
        # Clean up
        $CryptoStream.Dispose()
        $MemoryStream.Dispose()
        $Decryptor.Dispose()
        $Cipher.Dispose()
        
        return $DecryptedJson
    }
    catch {
        Write-Host "[ERROR] Decryption failed: $($_.Exception.Message)" -ForegroundColor Red
        throw
    }
}

# Perform decryption
Write-Host "Decrypting audit results..." -ForegroundColor Yellow
try {
    $DecryptedJson = Unprotect-JsonData -EncryptedPackage $EncryptedData -Password $Password
    $DecryptedData = $DecryptedJson | ConvertFrom-Json
    Write-Host "[SUCCESS] Audit results decrypted successfully" -ForegroundColor Green
} catch {
    Write-Host "[ERROR] Failed to decrypt audit results: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "[INFO] Please verify the password is correct" -ForegroundColor Yellow
    exit 1
}

# Optional deobfuscation
if ($DeobfuscateFields) {
    Write-Host "Deobfuscating field names..." -ForegroundColor Yellow
    
    # Check if deobfuscator script exists
    $DeobfuscatorPath = ".\Audit-Result-Deobfuscator.ps1"
    if (Test-Path $DeobfuscatorPath) {
        # Save decrypted data to temp file for deobfuscation
        $TempPath = [System.IO.Path]::GetTempFileName() + ".json"
        $DecryptedJson | Out-File -FilePath $TempPath -Encoding UTF8
        
        try {
            # Run deobfuscation
            $DeobfuscatedData = & $DeobfuscatorPath -ObfuscatedJsonPath $TempPath
            $DecryptedData = $DeobfuscatedData
            $DecryptedJson = $DecryptedData | ConvertTo-Json -Depth 20
            Write-Host "[SUCCESS] Field names deobfuscated" -ForegroundColor Green
        } catch {
            Write-Host "[WARNING] Deobfuscation failed: $($_.Exception.Message)" -ForegroundColor Yellow
        } finally {
            # Clean up temp file
            if (Test-Path $TempPath) { Remove-Item $TempPath -Force }
        }
    } else {
        Write-Host "[WARNING] Deobfuscator script not found at $DeobfuscatorPath" -ForegroundColor Yellow
    }
}

# Output results
if ($OutputPath) {
    # Save to file
    try {
        Write-Host "Saving decrypted results to file..." -ForegroundColor Yellow
        $DecryptedJson | Out-File -FilePath $OutputPath -Encoding UTF8 -ErrorAction Stop
        Write-Host "[SUCCESS] Decrypted results saved to: $OutputPath" -ForegroundColor Green
    } catch {
        Write-Host "[ERROR] Failed to save decrypted results: $($_.Exception.Message)" -ForegroundColor Red
        exit 1
    }
} else {
    # Display summary
    Write-Host ""
    Write-Host "DECRYPTION SUMMARY:" -ForegroundColor Yellow
    Write-Host "- Decryption Algorithm: $($EncryptedData.Algorithm)" -ForegroundColor White
    Write-Host "- Original Size: $($EncryptedData.DataLength) bytes" -ForegroundColor White
    Write-Host "- Encrypted Size: $($EncryptedData.EncryptedLength) bytes" -ForegroundColor White
    Write-Host "- Compression Ratio: $([math]::Round(($EncryptedData.EncryptedLength / $EncryptedData.DataLength) * 100, 1))%" -ForegroundColor White
    
    # Check for key sections
    if ($DecryptedData.PSObject.Properties.Name -contains "Section_A7B2" -or $DecryptedData.PSObject.Properties.Name -contains "SystemIdentity") {
        Write-Host "- SystemIdentity section: ✅ Found" -ForegroundColor Green
    }
    
    if ($DecryptedData.PSObject.Properties.Name -contains "Meta_X9K1" -or $DecryptedData.PSObject.Properties.Name -contains "AuditSignature") {
        Write-Host "- AuditSignature section: ✅ Found" -ForegroundColor Green
    }
    
    # Count security controls
    $SecurityControls = $DecryptedData.PSObject.Properties.Name | Where-Object { $_ -like "Control_*" -or $_ -like "WIN_*" }
    Write-Host "- Security Controls: $($SecurityControls.Count) found" -ForegroundColor White
    
    Write-Host ""
    Write-Host "Use -OutputPath parameter to save decrypted results to a file" -ForegroundColor Yellow
    Write-Host "Use -DeobfuscateFields to also deobfuscate field names" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "=================================================================================" -ForegroundColor Green
Write-Host "Decryption Complete" -ForegroundColor Green
Write-Host "=================================================================================" -ForegroundColor Green

<#
================================================================================
USAGE EXAMPLES:
================================================================================

1. BASIC DECRYPTION (Display summary):
   .\Decrypt-AuditResults.ps1 -EncryptedFilePath "Windows-Server-2019-Security-Results.encrypted.json"

2. DECRYPT AND SAVE TO FILE:
   .\Decrypt-AuditResults.ps1 -EncryptedFilePath "audit.encrypted.json" -OutputPath "decrypted-audit.json"

3. DECRYPT WITH PASSWORD:
   .\Decrypt-AuditResults.ps1 -EncryptedFilePath "audit.encrypted.json" -Password "SecurePassword123!"

4. DECRYPT AND DEOBFUSCATE:
   .\Decrypt-AuditResults.ps1 -EncryptedFilePath "audit.encrypted.json" -DeobfuscateFields -OutputPath "readable-audit.json"

SECURITY NOTES:
- This script is for authorized audit analysis only
- Ensure secure handling of decryption passwords
- Decrypted files should be stored securely
- Consider using secure deletion for temporary files

INTEGRITY VALIDATION:
After decryption and optional deobfuscation, you can validate integrity:

$DecryptedData = Get-Content "decrypted-audit.json" | ConvertFrom-Json
if ($DecryptedData.AuditSignature.AuditIntegrityHashes) {
    # Use Test-SystemIntegrity function to validate results
}

================================================================================
#>
