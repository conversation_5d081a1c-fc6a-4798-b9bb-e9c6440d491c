#Requires -Version 5.1
<#
================================================================================
Obfuscation Test Script
================================================================================
Description: Tests the obfuscation functionality of the audit script
Version: 1.0
Created: January 9, 2025

USAGE: .\Test-Obfuscation.ps1
================================================================================
#>

Write-Host "=================================================================================" -ForegroundColor Green
Write-Host "Audit Obfuscation Test" -ForegroundColor Green
Write-Host "=================================================================================" -ForegroundColor Green

# Test data structure similar to audit results
$TestData = @{
    SystemIdentity = @{
        ComputerName = "TEST-SERVER-01"
        MachineGUID = "12345678-1234-1234-1234-123456789012"
        CompositeHardwareFingerprint = "ABC123DEF456GHI789JKL012MNO345PQR678STU901VWX234YZ="
        HardwareFingerprint = @{
            CPU = @{
                ProcessorId = "BFEBFBFF000906E9"
                Manufacturer = "GenuineIntel"
                MaxClockSpeed = 2400
            }
            Motherboard = @{
                BaseBoardSerialNumber = "MB123456789"
                BIOSSerialNumber = "BIOS987654321"
            }
            Memory = @(
                @{
                    SerialNumber = "MEM001"
                    Manufacturer = "Samsung"
                    Capacity = 8589934592
                }
            )
        }
        IntegrityHashes = @{
            SHA256 = "E3B0C44298FC1C149AFBF4C8996FB92427AE41E4649B934CA495991B7852B855"
            SHA512 = "CF83E1357EEF8B63F4C7AD8C68C69D81B3F9E4F5A5C5E5E5E5E5E5E5E5E5E5E5"
        }
    }
    AuditSignature = @{
        AuditIntegrityHashes = @{
            SHA256 = "D2D2D2D2D2D2D2D2D2D2D2D2D2D2D2D2D2D2D2D2D2D2D2D2D2D2D2D2D2D2D2D2"
            DataLength = 12345
        }
        SignedBy = "DOMAIN\AuditUser"
    }
    WIN_1_1_WindowsUpdate = @{
        ControlID = "WIN-1.1"
        ControlName = "Windows Update and Patch Management"
        RiskLevel = "Critical"
        Finding = "Windows Update service active"
    }
}

# Load obfuscation mapping (simplified version for testing)
$ObfuscationMap = @{
    'SystemIdentity' = 'Section_A7B2'
    'AuditSignature' = 'Meta_X9K1'
    'WIN_1_1_WindowsUpdate' = 'Control_W1M2'
    'ComputerName' = 'Sys_ID_001'
    'MachineGUID' = 'Mach_ID_003'
    'CompositeHardwareFingerprint' = 'CHF_Hash_012'
    'HardwareFingerprint' = 'HW_Print_011'
    'CPU' = 'HW_CPU_001'
    'Motherboard' = 'HW_MB_002'
    'Memory' = 'HW_MEM_003'
    'ProcessorId' = 'CPU_ID_001'
    'Manufacturer' = 'CPU_MFG_006'
    'MaxClockSpeed' = 'CPU_CLK_007'
    'BaseBoardSerialNumber' = 'MB_SN_001'
    'BIOSSerialNumber' = 'BIOS_SN_005'
    'SerialNumber' = 'MEM_SN_001'
    'Capacity' = 'MEM_CAP_004'
    'IntegrityHashes' = 'Int_Hash_014'
    'SHA256' = 'Hash_SHA256_001'
    'SHA512' = 'Hash_SHA512_002'
    'AuditIntegrityHashes' = 'Sig_Hash_001'
    'DataLength' = 'Data_Len_005'
    'SignedBy' = 'Sig_By_003'
    'ControlID' = 'Ctrl_ID_001'
    'ControlName' = 'Ctrl_Name_002'
    'RiskLevel' = 'Risk_Level_003'
    'Finding' = 'Finding_009'
}

function ConvertTo-ObfuscatedHashtable {
    param([hashtable]$InputHashtable)
    
    $ObfuscatedHashtable = @{}
    
    foreach ($Key in $InputHashtable.Keys) {
        $ObfuscatedKey = if ($ObfuscationMap.ContainsKey($Key)) { $ObfuscationMap[$Key] } else { $Key }
        $Value = $InputHashtable[$Key]
        
        # Recursively obfuscate nested hashtables
        if ($Value -is [hashtable]) {
            $ObfuscatedHashtable[$ObfuscatedKey] = ConvertTo-ObfuscatedHashtable -InputHashtable $Value
        }
        # Handle arrays of hashtables
        elseif ($Value -is [array] -and $Value.Count -gt 0 -and $Value[0] -is [hashtable]) {
            $ObfuscatedHashtable[$ObfuscatedKey] = @()
            foreach ($Item in $Value) {
                if ($Item -is [hashtable]) {
                    $ObfuscatedHashtable[$ObfuscatedKey] += ConvertTo-ObfuscatedHashtable -InputHashtable $Item
                } else {
                    $ObfuscatedHashtable[$ObfuscatedKey] += $Item
                }
            }
        }
        else {
            $ObfuscatedHashtable[$ObfuscatedKey] = $Value
        }
    }
    
    return $ObfuscatedHashtable
}

Write-Host "🔧 TESTING OBFUSCATION FUNCTIONALITY" -ForegroundColor Yellow
Write-Host ""

# Test 1: Basic obfuscation
Write-Host "Test 1: Basic field obfuscation..." -ForegroundColor Cyan
$ObfuscatedData = ConvertTo-ObfuscatedHashtable -InputHashtable $TestData

# Check that sensitive field names are obfuscated
$Test1Results = @{
    SystemIdentityObfuscated = $ObfuscatedData.ContainsKey('Section_A7B2') -and -not $ObfuscatedData.ContainsKey('SystemIdentity')
    AuditSignatureObfuscated = $ObfuscatedData.ContainsKey('Meta_X9K1') -and -not $ObfuscatedData.ContainsKey('AuditSignature')
    ControlObfuscated = $ObfuscatedData.ContainsKey('Control_W1M2') -and -not $ObfuscatedData.ContainsKey('WIN_1_1_WindowsUpdate')
}

foreach ($TestName in $Test1Results.Keys) {
    $Result = $Test1Results[$TestName]
    $Status = if ($Result) { "✅ PASS" } else { "❌ FAIL" }
    Write-Host "  $TestName : $Status" -ForegroundColor $(if ($Result) { "Green" } else { "Red" })
}

# Test 2: Nested field obfuscation
Write-Host ""
Write-Host "Test 2: Nested field obfuscation..." -ForegroundColor Cyan
$SystemSection = $ObfuscatedData['Section_A7B2']
$Test2Results = @{
    ComputerNameObfuscated = $SystemSection.ContainsKey('Sys_ID_001') -and -not $SystemSection.ContainsKey('ComputerName')
    HardwareFingerprintObfuscated = $SystemSection.ContainsKey('HW_Print_011') -and -not $SystemSection.ContainsKey('HardwareFingerprint')
    CPUObfuscated = $SystemSection['HW_Print_011'].ContainsKey('HW_CPU_001') -and -not $SystemSection['HW_Print_011'].ContainsKey('CPU')
}

foreach ($TestName in $Test2Results.Keys) {
    $Result = $Test2Results[$TestName]
    $Status = if ($Result) { "✅ PASS" } else { "❌ FAIL" }
    Write-Host "  $TestName : $Status" -ForegroundColor $(if ($Result) { "Green" } else { "Red" })
}

# Test 3: Value preservation
Write-Host ""
Write-Host "Test 3: Value preservation..." -ForegroundColor Cyan
$Test3Results = @{
    ComputerNameValue = $SystemSection['Sys_ID_001'] -eq "TEST-SERVER-01"
    MachineGUIDValue = $SystemSection['Mach_ID_003'] -eq "12345678-1234-1234-1234-123456789012"
    ProcessorIdValue = $SystemSection['HW_Print_011']['HW_CPU_001']['CPU_ID_001'] -eq "BFEBFBFF000906E9"
}

foreach ($TestName in $Test3Results.Keys) {
    $Result = $Test3Results[$TestName]
    $Status = if ($Result) { "✅ PASS" } else { "❌ FAIL" }
    Write-Host "  $TestName : $Status" -ForegroundColor $(if ($Result) { "Green" } else { "Red" })
}

# Test 4: Array handling
Write-Host ""
Write-Host "Test 4: Array obfuscation..." -ForegroundColor Cyan
$MemoryArray = $SystemSection['HW_Print_011']['HW_MEM_003']
$Test4Results = @{
    ArrayPreserved = $MemoryArray -is [array] -and $MemoryArray.Count -eq 1
    ArrayItemObfuscated = $MemoryArray[0].ContainsKey('MEM_SN_001') -and -not $MemoryArray[0].ContainsKey('SerialNumber')
    ArrayValuePreserved = $MemoryArray[0]['MEM_SN_001'] -eq "MEM001"
}

foreach ($TestName in $Test4Results.Keys) {
    $Result = $Test4Results[$TestName]
    $Status = if ($Result) { "✅ PASS" } else { "❌ FAIL" }
    Write-Host "  $TestName : $Status" -ForegroundColor $(if ($Result) { "Green" } else { "Red" })
}

# Display obfuscated structure sample
Write-Host ""
Write-Host "🔍 OBFUSCATED STRUCTURE SAMPLE:" -ForegroundColor Yellow
Write-Host "Original: SystemIdentity.HardwareFingerprint.CPU.ProcessorId" -ForegroundColor White
Write-Host "Obfuscated: Section_A7B2.HW_Print_011.HW_CPU_001.CPU_ID_001" -ForegroundColor Cyan
Write-Host "Value: $($SystemSection['HW_Print_011']['HW_CPU_001']['CPU_ID_001'])" -ForegroundColor Green

# Overall test result
$AllTests = $Test1Results.Values + $Test2Results.Values + $Test3Results.Values + $Test4Results.Values
$PassedTests = ($AllTests | Where-Object { $_ -eq $true }).Count
$TotalTests = $AllTests.Count

Write-Host ""
Write-Host "=================================================================================" -ForegroundColor Green
Write-Host "OBFUSCATION TEST RESULTS" -ForegroundColor Green
Write-Host "=================================================================================" -ForegroundColor Green
Write-Host "Tests Passed: $PassedTests / $TotalTests" -ForegroundColor $(if ($PassedTests -eq $TotalTests) { "Green" } else { "Red" })

if ($PassedTests -eq $TotalTests) {
    Write-Host "✅ ALL TESTS PASSED - Obfuscation is working correctly!" -ForegroundColor Green
} else {
    Write-Host "❌ SOME TESTS FAILED - Review obfuscation implementation" -ForegroundColor Red
}

Write-Host "=================================================================================" -ForegroundColor Green
