#Requires -Version 5.1
<#
================================================================================
Audit Result De-obfuscator Script
================================================================================
Description: De-obfuscates Windows Server security audit results for analysis
Version: 1.0
Created: January 9, 2025

USAGE:
1. .\Audit-Result-Deobfuscator.ps1 -ObfuscatedJsonPath "audit-results.json"
2. .\Audit-Result-Deobfuscator.ps1 -ObfuscatedJsonPath "audit-results.json" -OutputPath "deobfuscated-results.json"

IMPORTANT: This script is for INTERNAL USE ONLY - Do not share with auditees
================================================================================
#>

[CmdletBinding()]
param(
    [Parameter(Mandatory = $true)]
    [string]$ObfuscatedJsonPath,
    
    [Parameter(Mandatory = $false)]
    [string]$OutputPath = $null,
    
    [Parameter(Mandatory = $false)]
    [string]$MappingDictionaryPath = ".\audit-field-mapping.json"
)

Write-Host "=================================================================================" -ForegroundColor Green
Write-Host "Audit Result De-obfuscator" -ForegroundColor Green
Write-Host "=================================================================================" -ForegroundColor Green
Write-Host "Obfuscated File: $ObfuscatedJsonPath" -ForegroundColor Cyan
Write-Host "Mapping Dictionary: $MappingDictionaryPath" -ForegroundColor Cyan
Write-Host "=================================================================================" -ForegroundColor Green

# Load the obfuscated audit results
try {
    Write-Host "Loading obfuscated audit results..." -ForegroundColor Yellow
    $ObfuscatedData = Get-Content -Path $ObfuscatedJsonPath -Raw | ConvertFrom-Json -ErrorAction Stop
    Write-Host "[SUCCESS] Obfuscated audit results loaded" -ForegroundColor Green
} catch {
    Write-Host "[ERROR] Failed to load obfuscated audit results: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Load the mapping dictionary
try {
    Write-Host "Loading field mapping dictionary..." -ForegroundColor Yellow
    $MappingData = Get-Content -Path $MappingDictionaryPath -Raw | ConvertFrom-Json -ErrorAction Stop
    Write-Host "[SUCCESS] Field mapping dictionary loaded" -ForegroundColor Green
} catch {
    Write-Host "[ERROR] Failed to load mapping dictionary: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Create reverse mapping hashtable for efficient lookup
Write-Host "Building reverse mapping lookup table..." -ForegroundColor Yellow
$ReverseMap = @{}

# Process all mapping sections
$MappingProperties = $MappingData.PSObject.Properties | Where-Object { $_.Name -like "*_mappings" }
foreach ($MappingProperty in $MappingProperties) {
    $MappingSection = $MappingProperty.Value
    $SectionProperties = $MappingSection.PSObject.Properties
    
    foreach ($Property in $SectionProperties) {
        $ObfuscatedName = $Property.Name
        $OriginalName = $Property.Value
        $ReverseMap[$ObfuscatedName] = $OriginalName
    }
}

Write-Host "[SUCCESS] Reverse mapping table built with $($ReverseMap.Count) entries" -ForegroundColor Green

# De-obfuscation function
function ConvertFrom-ObfuscatedObject {
    param($InputObject)
    
    if ($InputObject -eq $null) {
        return $null
    }
    
    # Handle arrays
    if ($InputObject -is [array]) {
        $DeobfuscatedArray = @()
        foreach ($Item in $InputObject) {
            $DeobfuscatedArray += ConvertFrom-ObfuscatedObject -InputObject $Item
        }
        return $DeobfuscatedArray
    }
    
    # Handle PSCustomObjects (from JSON)
    if ($InputObject -is [PSCustomObject]) {
        $DeobfuscatedHashtable = @{}
        
        $Properties = $InputObject.PSObject.Properties
        foreach ($Property in $Properties) {
            $ObfuscatedKey = $Property.Name
            $Value = $Property.Value
            
            # Look up the original field name
            $OriginalKey = if ($ReverseMap.ContainsKey($ObfuscatedKey)) { 
                $ReverseMap[$ObfuscatedKey] 
            } else { 
                $ObfuscatedKey  # Keep original if no mapping found
            }
            
            # Recursively de-obfuscate the value
            $DeobfuscatedHashtable[$OriginalKey] = ConvertFrom-ObfuscatedObject -InputObject $Value
        }
        
        return $DeobfuscatedHashtable
    }
    
    # Handle primitive values (strings, numbers, booleans)
    return $InputObject
}

# Perform de-obfuscation
Write-Host "De-obfuscating audit results..." -ForegroundColor Yellow
try {
    $DeobfuscatedResults = ConvertFrom-ObfuscatedObject -InputObject $ObfuscatedData
    Write-Host "[SUCCESS] Audit results de-obfuscated successfully" -ForegroundColor Green
} catch {
    Write-Host "[ERROR] Failed to de-obfuscate audit results: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Output results
if ($OutputPath) {
    # Save to file
    try {
        Write-Host "Saving de-obfuscated results to file..." -ForegroundColor Yellow
        $DeobfuscatedJson = $DeobfuscatedResults | ConvertTo-Json -Depth 20
        $DeobfuscatedJson | Out-File -FilePath $OutputPath -Encoding UTF8 -ErrorAction Stop
        Write-Host "[SUCCESS] De-obfuscated results saved to: $OutputPath" -ForegroundColor Green
    } catch {
        Write-Host "[ERROR] Failed to save de-obfuscated results: $($_.Exception.Message)" -ForegroundColor Red
        exit 1
    }
} else {
    # Display summary
    Write-Host ""
    Write-Host "DE-OBFUSCATION SUMMARY:" -ForegroundColor Yellow
    Write-Host "- Original sections found: $($DeobfuscatedResults.Keys.Count)" -ForegroundColor White
    
    if ($DeobfuscatedResults.ContainsKey("SystemIdentity")) {
        Write-Host "- SystemIdentity section: ✅ De-obfuscated" -ForegroundColor Green
        if ($DeobfuscatedResults.SystemIdentity.CompositeHardwareFingerprint) {
            Write-Host "  Hardware Fingerprint: $($DeobfuscatedResults.SystemIdentity.CompositeHardwareFingerprint.Substring(0,32))..." -ForegroundColor Cyan
        }
    }
    
    if ($DeobfuscatedResults.ContainsKey("AuditSignature")) {
        Write-Host "- AuditSignature section: ✅ De-obfuscated" -ForegroundColor Green
        if ($DeobfuscatedResults.AuditSignature.AuditIntegrityHashes) {
            Write-Host "  Integrity Hash: $($DeobfuscatedResults.AuditSignature.AuditIntegrityHashes.SHA256.Substring(0,32))..." -ForegroundColor Cyan
        }
    }
    
    # Count security controls
    $SecurityControls = $DeobfuscatedResults.Keys | Where-Object { $_ -like "WIN_*" }
    Write-Host "- Security Controls: $($SecurityControls.Count) found" -ForegroundColor White
    
    Write-Host ""
    Write-Host "Use -OutputPath parameter to save de-obfuscated results to a file" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "=================================================================================" -ForegroundColor Green
Write-Host "De-obfuscation Complete" -ForegroundColor Green
Write-Host "=================================================================================" -ForegroundColor Green

<#
================================================================================
USAGE EXAMPLES:
================================================================================

1. BASIC DE-OBFUSCATION (Display summary):
   .\Audit-Result-Deobfuscator.ps1 -ObfuscatedJsonPath "Windows-Server-2019-Security-Results.json"

2. DE-OBFUSCATE AND SAVE TO FILE:
   .\Audit-Result-Deobfuscator.ps1 -ObfuscatedJsonPath "Windows-Server-2019-Security-Results.json" -OutputPath "readable-results.json"

3. CUSTOM MAPPING DICTIONARY:
   .\Audit-Result-Deobfuscator.ps1 -ObfuscatedJsonPath "audit.json" -MappingDictionaryPath "custom-mapping.json"

SECURITY NOTES:
- This script is for INTERNAL USE ONLY
- Do not share this script or the mapping dictionary with auditees
- The mapping dictionary reveals the audit methodology
- Keep both files secure and access-controlled

INTEGRITY VALIDATION EXAMPLE:
After de-obfuscation, you can validate the audit results integrity:

$DeobfuscatedData = Get-Content "readable-results.json" | ConvertFrom-Json
if ($DeobfuscatedData.AuditSignature.AuditIntegrityHashes) {
    # Use the Test-SystemIntegrity function from the original audit script
    # to validate the results haven't been tampered with
}

================================================================================
#>
