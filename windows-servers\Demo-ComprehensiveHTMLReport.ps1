#Requires -Version 5.1
<#
================================================================================
Demo Script: Comprehensive HTML Report Generation
================================================================================
Description: Demonstrates how to convert deobfuscated Windows Server security 
             audit results to a comprehensive HTML report format
Version: 1.0
Created: January 9, 2025

This script demonstrates the complete workflow:
1. Load obfuscated audit results
2. Deobfuscate the results using the mapping dictionary
3. Convert to comprehensive HTML report with E.Z. Consultancy branding
================================================================================
#>

Write-Host "=================================================================================" -ForegroundColor Green
Write-Host "Demo: Comprehensive HTML Report Generation Workflow" -ForegroundColor Green
Write-Host "=================================================================================" -ForegroundColor Green

# Define file paths
$ObfuscatedJsonPath = ".\Windows-Server-2019-Security-Results.json"
$DeobfuscatedJsonPath = ".\Deobfuscated-Audit-Results-Demo.json"
$ComprehensiveHtmlPath = ".\Comprehensive-Windows-Server-Audit-Report-Demo.html"
$MappingDictionaryPath = ".\audit-field-mapping.json"

Write-Host "Step 1: Checking for required files..." -ForegroundColor Yellow

# Check if files exist
$FilesExist = $true

if (-not (Test-Path $ObfuscatedJsonPath)) {
    Write-Host "[WARNING] Obfuscated audit results not found: $ObfuscatedJsonPath" -ForegroundColor Yellow
    $FilesExist = $false
}

if (-not (Test-Path $MappingDictionaryPath)) {
    Write-Host "[WARNING] Mapping dictionary not found: $MappingDictionaryPath" -ForegroundColor Yellow
    $FilesExist = $false
}

if (-not $FilesExist) {
    Write-Host ""
    Write-Host "MISSING FILES DETECTED:" -ForegroundColor Red
    Write-Host "This demo requires the following files to be present:" -ForegroundColor Yellow
    Write-Host "1. $ObfuscatedJsonPath - Obfuscated audit results from the Windows Server audit script" -ForegroundColor White
    Write-Host "2. $MappingDictionaryPath - Field mapping dictionary for deobfuscation" -ForegroundColor White
    Write-Host ""
    Write-Host "To generate these files:" -ForegroundColor Cyan
    Write-Host "1. Run the Windows Server 2019 Security Audit script on a target system" -ForegroundColor White
    Write-Host "2. Ensure the audit-field-mapping.json file is present" -ForegroundColor White
    Write-Host ""
    Write-Host "Alternatively, you can run this demo with existing deobfuscated results:" -ForegroundColor Cyan
    Write-Host ".\Convert-AuditResults-ToComprehensiveHTML.ps1 -JsonPath 'Deobfuscated-Audit-Results.json'" -ForegroundColor White
    Write-Host ""
    exit 1
}

Write-Host "[SUCCESS] Required files found" -ForegroundColor Green

# Step 2: Deobfuscate the audit results
Write-Host ""
Write-Host "Step 2: Deobfuscating audit results..." -ForegroundColor Yellow

try {
    # Run the deobfuscation script
    & ".\Audit-Result-Deobfuscator.ps1" -ObfuscatedJsonPath $ObfuscatedJsonPath -OutputPath $DeobfuscatedJsonPath -MappingDictionaryPath $MappingDictionaryPath
    
    if (Test-Path $DeobfuscatedJsonPath) {
        Write-Host "[SUCCESS] Audit results deobfuscated successfully" -ForegroundColor Green
    } else {
        throw "Deobfuscated file was not created"
    }
} catch {
    Write-Host "[ERROR] Failed to deobfuscate audit results: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Step 3: Generate comprehensive HTML report
Write-Host ""
Write-Host "Step 3: Generating comprehensive HTML report..." -ForegroundColor Yellow

try {
    # Run the comprehensive HTML converter
    & ".\Convert-AuditResults-ToComprehensiveHTML.ps1" -JsonPath $DeobfuscatedJsonPath -OutputPath $ComprehensiveHtmlPath -CompanyName "E.Z. Consultancy" -ReportTitle "Windows Server 2019 Security Audit Report" -IncludeDetailedTechnicalData
    
    if (Test-Path $ComprehensiveHtmlPath) {
        Write-Host "[SUCCESS] Comprehensive HTML report generated successfully" -ForegroundColor Green
    } else {
        throw "HTML report file was not created"
    }
} catch {
    Write-Host "[ERROR] Failed to generate HTML report: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Step 4: Display results and next steps
Write-Host ""
Write-Host "=================================================================================" -ForegroundColor Green
Write-Host "Demo Workflow Complete!" -ForegroundColor Green
Write-Host "=================================================================================" -ForegroundColor Green

Write-Host ""
Write-Host "GENERATED FILES:" -ForegroundColor Yellow
Write-Host "1. Deobfuscated Results: $DeobfuscatedJsonPath" -ForegroundColor White
Write-Host "2. Comprehensive HTML Report: $ComprehensiveHtmlPath" -ForegroundColor White

Write-Host ""
Write-Host "REPORT FEATURES INCLUDED:" -ForegroundColor Yellow
Write-Host "✓ Executive Summary with compliance metrics and key findings" -ForegroundColor Green
Write-Host "✓ Risk-based categorization (High/Medium/Low)" -ForegroundColor Green
Write-Host "✓ Detailed findings with structured format:" -ForegroundColor Green
Write-Host "  - Title (15 words max)" -ForegroundColor White
Write-Host "  - Finding (100 words)" -ForegroundColor White
Write-Host "  - 2 Root Causes (25 words each)" -ForegroundColor White
Write-Host "  - 2 Risks (25 words each)" -ForegroundColor White
Write-Host "  - Recommendation (100 words)" -ForegroundColor White
Write-Host "  - Meeting Brief (20 words)" -ForegroundColor White
Write-Host "  - Best Practice (20 words, passive voice)" -ForegroundColor White
Write-Host "  - Risk Rating using Impact×Possibility×Frequency formula" -ForegroundColor White
Write-Host "✓ Professional styling with responsive design" -ForegroundColor Green
Write-Host "✓ Hardware fingerprinting and system identification" -ForegroundColor Green
Write-Host "✓ Audit integrity verification with tamper detection" -ForegroundColor Green
Write-Host "✓ E.Z. Consultancy branding and signature block" -ForegroundColor Green
Write-Host "✓ Print-friendly layout with proper page breaks" -ForegroundColor Green
Write-Host "✓ Interactive navigation tabs" -ForegroundColor Green
Write-Host "✓ Color-coded risk levels and compliance status" -ForegroundColor Green

Write-Host ""
Write-Host "NEXT STEPS:" -ForegroundColor Yellow
Write-Host "1. Open the HTML report in any web browser:" -ForegroundColor White
Write-Host "   $ComprehensiveHtmlPath" -ForegroundColor Cyan
Write-Host "2. Review the executive summary for key findings" -ForegroundColor White
Write-Host "3. Navigate through the detailed findings using the tabs" -ForegroundColor White
Write-Host "4. Use the print function for hard copy distribution" -ForegroundColor White
Write-Host "5. Share with stakeholders for remediation planning" -ForegroundColor White

Write-Host ""
Write-Host "CUSTOMIZATION OPTIONS:" -ForegroundColor Yellow
Write-Host "- Company Name: Modify the -CompanyName parameter" -ForegroundColor White
Write-Host "- Report Title: Customize the -ReportTitle parameter" -ForegroundColor White
Write-Host "- Technical Data: Use -IncludeDetailedTechnicalData switch for raw data" -ForegroundColor White
Write-Host "- Output Path: Specify custom location with -OutputPath parameter" -ForegroundColor White

Write-Host ""
Write-Host "EXAMPLE USAGE:" -ForegroundColor Yellow
Write-Host ".\Convert-AuditResults-ToComprehensiveHTML.ps1 \\" -ForegroundColor Cyan
Write-Host "  -JsonPath 'Deobfuscated-Audit-Results.json' \\" -ForegroundColor Cyan
Write-Host "  -OutputPath 'Custom-Audit-Report.html' \\" -ForegroundColor Cyan
Write-Host "  -CompanyName 'Your Company Name' \\" -ForegroundColor Cyan
Write-Host "  -ReportTitle 'Custom Report Title' \\" -ForegroundColor Cyan
Write-Host "  -IncludeDetailedTechnicalData" -ForegroundColor Cyan

Write-Host ""
Write-Host "=================================================================================" -ForegroundColor Green

# Optional: Open the HTML report in default browser
$OpenInBrowser = Read-Host "Would you like to open the HTML report in your default browser? (Y/N)"
if ($OpenInBrowser -eq 'Y' -or $OpenInBrowser -eq 'y') {
    try {
        Start-Process $ComprehensiveHtmlPath
        Write-Host "[SUCCESS] HTML report opened in default browser" -ForegroundColor Green
    } catch {
        Write-Host "[WARNING] Could not open browser automatically. Please open the file manually:" -ForegroundColor Yellow
        Write-Host "$ComprehensiveHtmlPath" -ForegroundColor Cyan
    }
}

Write-Host ""
Write-Host "Demo completed successfully!" -ForegroundColor Green
Write-Host "=================================================================================" -ForegroundColor Green
