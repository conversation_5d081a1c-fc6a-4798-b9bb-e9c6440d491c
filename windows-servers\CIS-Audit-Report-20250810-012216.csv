﻿ComputerName,ControlID,ControlName,Description,Type,CurrentValue,RecommendedValue,ComplianceStatus,RiskLevel,Remediation,AuditDate
PRD,1.1,Asset Inventory - Network Discovery,Function Discovery Resource Publication service should be disabled,Service,Manual,4,Non-Compliant,Medium,Set-Service -Name 'FDResPub' -StartupType Disabled,8/10/2025 1:22
PRD,1.2,Asset Inventory - SSDP Discovery,SSDP Discovery service should be disabled,Service,Manual,4,Non-Compliant,Medium,Set-Service -Name 'SSDPSRV' -StartupType Disabled,8/10/2025 1:22
PRD,2.1,Software Inventory - Windows Installer,Always install with elevated privileges should be disabled,Registry,,0,Not Configured,High,New-Item -Path 'HKLM:\SOFTWARE\Policies\Microsoft\Windows\Installer' -Force; Set-ItemProperty -Path 'HKLM:\SOFTWARE\Policies\Microsoft\Windows\Installer' -Name 'AlwaysInstallElevated' -Value 0,8/10/2025 1:22
PRD,3.1,Data Protection - BitLocker,BitLocker should require additional authentication at startup,Registry,,1,Not Configured,High,New-Item -Path 'HKLM:\SOFTWARE\Policies\Microsoft\FVE' -Force; Set-ItemProperty -Path 'HKLM:\SOFTWARE\Policies\Microsoft\FVE' -Name 'UseAdvancedStartup' -Value 1,8/10/2025 1:22
PRD,4.1,Secure Configuration - Guest Account,Guest account should be disabled,SecurityPolicy,TRUE,TRUE,Compliant,High,,8/10/2025 1:22
PRD,4.2,Secure Configuration - Anonymous SID Enumeration,Do not allow anonymous enumeration of SAM accounts,Registry,1,1,Compliant,Medium,,8/10/2025 1:22
PRD,5.1,Account Management - Password Policy,Minimum password length should be 14 characters,SecurityPolicy,,14,Not Configured,High,,8/10/2025 1:22
PRD,5.2,Account Management - Account Lockout,Account lockout threshold should be 5 or fewer invalid attempts,SecurityPolicy,,5,Not Configured,Medium,,8/10/2025 1:22
PRD,6.1,Access Control - User Rights Assignment,Access this computer from the network user right assignment,SecurityPolicy,,System.Object[],Not Configured,High,,8/10/2025 1:22
PRD,7.1,Vulnerability Management - Windows Update,Configure Automatic Updates should be enabled,Registry,,0,Not Configured,High,New-Item -Path 'HKLM:\SOFTWARE\Policies\Microsoft\Windows\WindowsUpdate\AU' -Force; Set-ItemProperty -Path 'HKLM:\SOFTWARE\Policies\Microsoft\Windows\WindowsUpdate\AU' -Name 'NoAutoUpdate' -Value 0,8/10/2025 1:22
PRD,8.1,Audit Log Management - Success Logon,Audit logon events should be configured for Success,AuditPolicy,,Success,Not Configured,Medium,,8/10/2025 1:22
PRD,8.2,Audit Log Management - Failure Logon,Audit logon events should be configured for Failure,AuditPolicy,,Failure,Not Configured,Medium,,8/10/2025 1:22
PRD,9.1,Email/Web Protection - SmartScreen,Windows Defender SmartScreen should be enabled,Registry,,1,Not Configured,Medium,New-Item -Path 'HKLM:\SOFTWARE\Policies\Microsoft\Windows\System' -Force; Set-ItemProperty -Path 'HKLM:\SOFTWARE\Policies\Microsoft\Windows\System' -Name 'EnableSmartScreen' -Value 1,8/10/2025 1:22
PRD,10.1,Malware Defenses - Windows Defender,Windows Defender Antivirus should be enabled,Registry,,0,Not Configured,High,New-Item -Path 'HKLM:\SOFTWARE\Policies\Microsoft\Windows Defender' -Force; Set-ItemProperty -Path 'HKLM:\SOFTWARE\Policies\Microsoft\Windows Defender' -Name 'DisableAntiSpyware' -Value 0,8/10/2025 1:22
PRD,10.2,Malware Defenses - Real-time Protection,Windows Defender real-time protection should be enabled,Registry,,0,Not Configured,High,New-Item -Path 'HKLM:\SOFTWARE\Policies\Microsoft\Windows Defender\Real-Time Protection' -Force; Set-ItemProperty -Path 'HKLM:\SOFTWARE\Policies\Microsoft\Windows Defender\Real-Time Protection' -Name 'DisableRealtimeMonitoring' -Value 0,8/10/2025 1:22
PRD,11.1,Data Recovery - System Restore,System Restore should be enabled,Registry,,0,Not Configured,Medium,New-Item -Path 'HKLM:\SOFTWARE\Microsoft\Windows NT\CurrentVersion\SystemRestore' -Force; Set-ItemProperty -Path 'HKLM:\SOFTWARE\Microsoft\Windows NT\CurrentVersion\SystemRestore' -Name 'DisableSR' -Value 0,8/10/2025 1:22
PRD,12.1,Network Infrastructure - Windows Firewall Domain,Windows Firewall Domain Profile should be enabled,Registry,,1,Not Configured,High,New-Item -Path 'HKLM:\SOFTWARE\Policies\Microsoft\WindowsFirewall\DomainProfile' -Force; Set-ItemProperty -Path 'HKLM:\SOFTWARE\Policies\Microsoft\WindowsFirewall\DomainProfile' -Name 'EnableFirewall' -Value 1,8/10/2025 1:22
PRD,12.2,Network Infrastructure - Windows Firewall Private,Windows Firewall Private Profile should be enabled,Registry,,1,Not Configured,High,New-Item -Path 'HKLM:\SOFTWARE\Policies\Microsoft\WindowsFirewall\PrivateProfile' -Force; Set-ItemProperty -Path 'HKLM:\SOFTWARE\Policies\Microsoft\WindowsFirewall\PrivateProfile' -Name 'EnableFirewall' -Value 1,8/10/2025 1:22
PRD,12.3,Network Infrastructure - Windows Firewall Public,Windows Firewall Public Profile should be enabled,Registry,,1,Not Configured,High,New-Item -Path 'HKLM:\SOFTWARE\Policies\Microsoft\WindowsFirewall\PublicProfile' -Force; Set-ItemProperty -Path 'HKLM:\SOFTWARE\Policies\Microsoft\WindowsFirewall\PublicProfile' -Name 'EnableFirewall' -Value 1,8/10/2025 1:22
PRD,16.1,Application Security - PowerShell Execution Policy,PowerShell execution policy should be RemoteSigned or more restrictive,Registry,Restricted,RemoteSigned,Non-Compliant,Medium,Set-ItemProperty -Path 'HKLM:\SOFTWARE\Microsoft\PowerShell\1\ShellIds\Microsoft.PowerShell' -Name 'ExecutionPolicy' -Value RemoteSigned,8/10/2025 1:22
