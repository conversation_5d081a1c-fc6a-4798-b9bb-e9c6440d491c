﻿{
    "Summary":  {
                    "Errors":  0,
                    "MediumRiskIssues":  3,
                    "HighRiskIssues":  0,
                    "Compliant":  2,
                    "NonCompliant":  3,
                    "NotConfigured":  15,
                    "TotalControls":  20,
                    "CompliancePercentage":  10
                },
    "Results":  [
                    {
                        "ComputerName":  "PRD",
                        "ControlID":  "10.1",
                        "ControlName":  "Malware Defenses - Windows Defender",
                        "Description":  "Windows Defender Antivirus should be enabled",
                        "Type":  "Registry",
                        "CurrentValue":  null,
                        "RecommendedValue":  0,
                        "ComplianceStatus":  "Not Configured",
                        "RiskLevel":  "High",
                        "Remediation":  "New-Item -Path \u0027HKLM:\\SOFTWARE\\Policies\\Microsoft\\Windows Defender\u0027 -Force; Set-ItemProperty -Path \u0027HKLM:\\SOFTWARE\\Policies\\Microsoft\\Windows Defender\u0027 -Name \u0027DisableAntiSpyware\u0027 -Value 0",
                        "AuditDate":  "\/Date(*************)\/"
                    },
                    {
                        "ComputerName":  "PRD",
                        "ControlID":  "12.1",
                        "ControlName":  "Network Infrastructure - Windows Firewall Domain",
                        "Description":  "Windows Firewall Domain Profile should be enabled",
                        "Type":  "Registry",
                        "CurrentValue":  null,
                        "RecommendedValue":  1,
                        "ComplianceStatus":  "Not Configured",
                        "RiskLevel":  "High",
                        "Remediation":  "New-Item -Path \u0027HKLM:\\SOFTWARE\\Policies\\Microsoft\\WindowsFirewall\\DomainProfile\u0027 -Force; Set-ItemProperty -Path \u0027HKLM:\\SOFTWARE\\Policies\\Microsoft\\WindowsFirewall\\DomainProfile\u0027 -Name \u0027EnableFirewall\u0027 -Value 1",
                        "AuditDate":  "\/Date(*************)\/"
                    },
                    {
                        "ComputerName":  "PRD",
                        "ControlID":  "12.2",
                        "ControlName":  "Network Infrastructure - Windows Firewall Private",
                        "Description":  "Windows Firewall Private Profile should be enabled",
                        "Type":  "Registry",
                        "CurrentValue":  null,
                        "RecommendedValue":  1,
                        "ComplianceStatus":  "Not Configured",
                        "RiskLevel":  "High",
                        "Remediation":  "New-Item -Path \u0027HKLM:\\SOFTWARE\\Policies\\Microsoft\\WindowsFirewall\\PrivateProfile\u0027 -Force; Set-ItemProperty -Path \u0027HKLM:\\SOFTWARE\\Policies\\Microsoft\\WindowsFirewall\\PrivateProfile\u0027 -Name \u0027EnableFirewall\u0027 -Value 1",
                        "AuditDate":  "\/Date(*************)\/"
                    },
                    {
                        "ComputerName":  "PRD",
                        "ControlID":  "12.3",
                        "ControlName":  "Network Infrastructure - Windows Firewall Public",
                        "Description":  "Windows Firewall Public Profile should be enabled",
                        "Type":  "Registry",
                        "CurrentValue":  null,
                        "RecommendedValue":  1,
                        "ComplianceStatus":  "Not Configured",
                        "RiskLevel":  "High",
                        "Remediation":  "New-Item -Path \u0027HKLM:\\SOFTWARE\\Policies\\Microsoft\\WindowsFirewall\\PublicProfile\u0027 -Force; Set-ItemProperty -Path \u0027HKLM:\\SOFTWARE\\Policies\\Microsoft\\WindowsFirewall\\PublicProfile\u0027 -Name \u0027EnableFirewall\u0027 -Value 1",
                        "AuditDate":  "\/Date(*************)\/"
                    },
                    {
                        "ComputerName":  "PRD",
                        "ControlID":  "9.1",
                        "ControlName":  "Email/Web Protection - SmartScreen",
                        "Description":  "Windows Defender SmartScreen should be enabled",
                        "Type":  "Registry",
                        "CurrentValue":  null,
                        "RecommendedValue":  1,
                        "ComplianceStatus":  "Not Configured",
                        "RiskLevel":  "Medium",
                        "Remediation":  "New-Item -Path \u0027HKLM:\\SOFTWARE\\Policies\\Microsoft\\Windows\\System\u0027 -Force; Set-ItemProperty -Path \u0027HKLM:\\SOFTWARE\\Policies\\Microsoft\\Windows\\System\u0027 -Name \u0027EnableSmartScreen\u0027 -Value 1",
                        "AuditDate":  "\/Date(*************)\/"
                    },
                    {
                        "ComputerName":  "PRD",
                        "ControlID":  "1.1",
                        "ControlName":  "Asset Inventory - Network Discovery",
                        "Description":  "Function Discovery Resource Publication service should be disabled",
                        "Type":  "Service",
                        "CurrentValue":  3,
                        "RecommendedValue":  4,
                        "ComplianceStatus":  "Non-Compliant",
                        "RiskLevel":  "Medium",
                        "Remediation":  "Set-Service -Name \u0027FDResPub\u0027 -StartupType Disabled",
                        "AuditDate":  "\/Date(*************)\/"
                    },
                    {
                        "ComputerName":  "PRD",
                        "ControlID":  "16.1",
                        "ControlName":  "Application Security - PowerShell Execution Policy",
                        "Description":  "PowerShell execution policy should be RemoteSigned or more restrictive",
                        "Type":  "Registry",
                        "CurrentValue":  "Restricted",
                        "RecommendedValue":  "RemoteSigned",
                        "ComplianceStatus":  "Non-Compliant",
                        "RiskLevel":  "Medium",
                        "Remediation":  "Set-ItemProperty -Path \u0027HKLM:\\SOFTWARE\\Microsoft\\PowerShell\\1\\ShellIds\\Microsoft.PowerShell\u0027 -Name \u0027ExecutionPolicy\u0027 -Value RemoteSigned",
                        "AuditDate":  "\/Date(*************)\/"
                    },
                    {
                        "ComputerName":  "PRD",
                        "ControlID":  "7.1",
                        "ControlName":  "Vulnerability Management - Windows Update",
                        "Description":  "Configure Automatic Updates should be enabled",
                        "Type":  "Registry",
                        "CurrentValue":  null,
                        "RecommendedValue":  0,
                        "ComplianceStatus":  "Not Configured",
                        "RiskLevel":  "High",
                        "Remediation":  "New-Item -Path \u0027HKLM:\\SOFTWARE\\Policies\\Microsoft\\Windows\\WindowsUpdate\\AU\u0027 -Force; Set-ItemProperty -Path \u0027HKLM:\\SOFTWARE\\Policies\\Microsoft\\Windows\\WindowsUpdate\\AU\u0027 -Name \u0027NoAutoUpdate\u0027 -Value 0",
                        "AuditDate":  "\/Date(*************)\/"
                    },
                    {
                        "ComputerName":  "PRD",
                        "ControlID":  "3.1",
                        "ControlName":  "Data Protection - BitLocker",
                        "Description":  "BitLocker should require additional authentication at startup",
                        "Type":  "Registry",
                        "CurrentValue":  null,
                        "RecommendedValue":  1,
                        "ComplianceStatus":  "Not Configured",
                        "RiskLevel":  "High",
                        "Remediation":  "New-Item -Path \u0027HKLM:\\SOFTWARE\\Policies\\Microsoft\\FVE\u0027 -Force; Set-ItemProperty -Path \u0027HKLM:\\SOFTWARE\\Policies\\Microsoft\\FVE\u0027 -Name \u0027UseAdvancedStartup\u0027 -Value 1",
                        "AuditDate":  "\/Date(*************)\/"
                    },
                    {
                        "ComputerName":  "PRD",
                        "ControlID":  "1.2",
                        "ControlName":  "Asset Inventory - SSDP Discovery",
                        "Description":  "SSDP Discovery service should be disabled",
                        "Type":  "Service",
                        "CurrentValue":  3,
                        "RecommendedValue":  4,
                        "ComplianceStatus":  "Non-Compliant",
                        "RiskLevel":  "Medium",
                        "Remediation":  "Set-Service -Name \u0027SSDPSRV\u0027 -StartupType Disabled",
                        "AuditDate":  "\/Date(*************)\/"
                    },
                    {
                        "ComputerName":  "PRD",
                        "ControlID":  "5.1",
                        "ControlName":  "Account Management - Password Policy",
                        "Description":  "Minimum password length should be 14 characters",
                        "Type":  "SecurityPolicy",
                        "CurrentValue":  null,
                        "RecommendedValue":  14,
                        "ComplianceStatus":  "Not Configured",
                        "RiskLevel":  "High",
                        "Remediation":  "",
                        "AuditDate":  "\/Date(*************)\/"
                    },
                    {
                        "ComputerName":  "PRD",
                        "ControlID":  "2.1",
                        "ControlName":  "Software Inventory - Windows Installer",
                        "Description":  "Always install with elevated privileges should be disabled",
                        "Type":  "Registry",
                        "CurrentValue":  null,
                        "RecommendedValue":  0,
                        "ComplianceStatus":  "Not Configured",
                        "RiskLevel":  "High",
                        "Remediation":  "New-Item -Path \u0027HKLM:\\SOFTWARE\\Policies\\Microsoft\\Windows\\Installer\u0027 -Force; Set-ItemProperty -Path \u0027HKLM:\\SOFTWARE\\Policies\\Microsoft\\Windows\\Installer\u0027 -Name \u0027AlwaysInstallElevated\u0027 -Value 0",
                        "AuditDate":  "\/Date(*************)\/"
                    },
                    {
                        "ComputerName":  "PRD",
                        "ControlID":  "8.1",
                        "ControlName":  "Audit Log Management - Success Logon",
                        "Description":  "Audit logon events should be configured for Success",
                        "Type":  "AuditPolicy",
                        "CurrentValue":  null,
                        "RecommendedValue":  "Success",
                        "ComplianceStatus":  "Not Configured",
                        "RiskLevel":  "Medium",
                        "Remediation":  "",
                        "AuditDate":  "\/Date(*************)\/"
                    },
                    {
                        "ComputerName":  "PRD",
                        "ControlID":  "4.1",
                        "ControlName":  "Secure Configuration - Guest Account",
                        "Description":  "Guest account should be disabled",
                        "Type":  "SecurityPolicy",
                        "CurrentValue":  true,
                        "RecommendedValue":  true,
                        "ComplianceStatus":  "Compliant",
                        "RiskLevel":  "High",
                        "Remediation":  "",
                        "AuditDate":  "\/Date(*************)\/"
                    },
                    {
                        "ComputerName":  "PRD",
                        "ControlID":  "11.1",
                        "ControlName":  "Data Recovery - System Restore",
                        "Description":  "System Restore should be enabled",
                        "Type":  "Registry",
                        "CurrentValue":  null,
                        "RecommendedValue":  0,
                        "ComplianceStatus":  "Not Configured",
                        "RiskLevel":  "Medium",
                        "Remediation":  "New-Item -Path \u0027HKLM:\\SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\SystemRestore\u0027 -Force; Set-ItemProperty -Path \u0027HKLM:\\SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\SystemRestore\u0027 -Name \u0027DisableSR\u0027 -Value 0",
                        "AuditDate":  "\/Date(*************)\/"
                    },
                    {
                        "ComputerName":  "PRD",
                        "ControlID":  "5.2",
                        "ControlName":  "Account Management - Account Lockout",
                        "Description":  "Account lockout threshold should be 5 or fewer invalid attempts",
                        "Type":  "SecurityPolicy",
                        "CurrentValue":  null,
                        "RecommendedValue":  5,
                        "ComplianceStatus":  "Not Configured",
                        "RiskLevel":  "Medium",
                        "Remediation":  "",
                        "AuditDate":  "\/Date(*************)\/"
                    },
                    {
                        "ComputerName":  "PRD",
                        "ControlID":  "6.1",
                        "ControlName":  "Access Control - User Rights Assignment",
                        "Description":  "Access this computer from the network user right assignment",
                        "Type":  "SecurityPolicy",
                        "CurrentValue":  null,
                        "RecommendedValue":  [
                                                 "Administrators",
                                                 "Authenticated Users"
                                             ],
                        "ComplianceStatus":  "Not Configured",
                        "RiskLevel":  "High",
                        "Remediation":  "",
                        "AuditDate":  "\/Date(*************)\/"
                    },
                    {
                        "ComputerName":  "PRD",
                        "ControlID":  "4.2",
                        "ControlName":  "Secure Configuration - Anonymous SID Enumeration",
                        "Description":  "Do not allow anonymous enumeration of SAM accounts",
                        "Type":  "Registry",
                        "CurrentValue":  1,
                        "RecommendedValue":  1,
                        "ComplianceStatus":  "Compliant",
                        "RiskLevel":  "Medium",
                        "Remediation":  "",
                        "AuditDate":  "\/Date(*************)\/"
                    },
                    {
                        "ComputerName":  "PRD",
                        "ControlID":  "8.2",
                        "ControlName":  "Audit Log Management - Failure Logon",
                        "Description":  "Audit logon events should be configured for Failure",
                        "Type":  "AuditPolicy",
                        "CurrentValue":  null,
                        "RecommendedValue":  "Failure",
                        "ComplianceStatus":  "Not Configured",
                        "RiskLevel":  "Medium",
                        "Remediation":  "",
                        "AuditDate":  "\/Date(*************)\/"
                    },
                    {
                        "ComputerName":  "PRD",
                        "ControlID":  "10.2",
                        "ControlName":  "Malware Defenses - Real-time Protection",
                        "Description":  "Windows Defender real-time protection should be enabled",
                        "Type":  "Registry",
                        "CurrentValue":  null,
                        "RecommendedValue":  0,
                        "ComplianceStatus":  "Not Configured",
                        "RiskLevel":  "High",
                        "Remediation":  "New-Item -Path \u0027HKLM:\\SOFTWARE\\Policies\\Microsoft\\Windows Defender\\Real-Time Protection\u0027 -Force; Set-ItemProperty -Path \u0027HKLM:\\SOFTWARE\\Policies\\Microsoft\\Windows Defender\\Real-Time Protection\u0027 -Name \u0027DisableRealtimeMonitoring\u0027 -Value 0",
                        "AuditDate":  "\/Date(*************)\/"
                    }
                ],
    "AuditMetadata":  {
                          "TargetOS":  "Windows Server 2019",
                          "TotalSystems":  null,
                          "GeneratedDate":  "2025-08-10 01:20:54",
                          "Framework":  "CIS Controls v8"
                      }
}
