#Requires -Version 5.1
<#
================================================================================
Comprehensive Audit Results HTML Report Generator
================================================================================
Description: Converts deobfuscated Windows Server security audit results to a 
             comprehensive, professional HTML report with enhanced formatting,
             executive summary, risk analysis, and E.Z. Consultancy branding
Version: 2.0
Created: January 9, 2025
Updated: January 9, 2025

USAGE:
.\Convert-AuditResults-ToComprehensiveHTML.ps1 -JsonPath "Deobfuscated-Audit-Results.json" -OutputPath "Comprehensive-Audit-Report.html"

FEATURES:
- Professional styling with responsive design
- Executive summary with key metrics and recommendations
- Risk-based categorization and color coding
- Detailed findings with root causes, risks, and recommendations
- Hardware fingerprinting and system identification
- Audit integrity verification information
- E.Z. Consultancy branding and signature block
- Print-friendly layout with proper page breaks
- Interactive elements for better navigation
================================================================================
#>

[CmdletBinding()]
param(
    [Parameter(Mandatory = $true)]
    [ValidateScript({Test-Path $_ -PathType Leaf})]
    [string]$JsonPath,
    
    [Parameter(Mandatory = $false)]
    [string]$OutputPath = "Comprehensive-Windows-Server-Audit-Report.html",
    
    [Parameter(Mandatory = $false)]
    [string]$CompanyName = "E.Z. Consultancy",
    
    [Parameter(Mandatory = $false)]
    [string]$ReportTitle = "Windows Server 2019 Security Audit Report",
    
    [Parameter(Mandatory = $false)]
    [switch]$IncludeDetailedTechnicalData
)

Write-Host "=================================================================================" -ForegroundColor Green
Write-Host "Comprehensive Audit Results HTML Report Generator" -ForegroundColor Green
Write-Host "=================================================================================" -ForegroundColor Green
Write-Host "Input JSON: $JsonPath" -ForegroundColor Cyan
Write-Host "Output HTML: $OutputPath" -ForegroundColor Cyan
Write-Host "Company: $CompanyName" -ForegroundColor Cyan
Write-Host "=================================================================================" -ForegroundColor Green

# Load the deobfuscated audit results
try {
    Write-Host "Loading deobfuscated audit results..." -ForegroundColor Yellow
    $AuditData = Get-Content -Path $JsonPath -Raw | ConvertFrom-Json -ErrorAction Stop
    Write-Host "[SUCCESS] Audit results loaded" -ForegroundColor Green
} catch {
    Write-Host "[ERROR] Failed to load audit results: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Helper functions for risk assessment and formatting
function Get-RiskRating {
    param($Impact, $Possibility, $Frequency)
    
    # Default values if not provided
    $Impact = if ($Impact) { $Impact } else { 3 }
    $Possibility = if ($Possibility) { $Possibility } else { 3 }
    $Frequency = if ($Frequency) { $Frequency } else { 3 }
    
    $RiskScore = $Impact * $Possibility * $Frequency
    
    if ($RiskScore -lt 17) { return @{ Level = "Low"; Color = "success"; Score = $RiskScore } }
    elseif ($RiskScore -le 36) { return @{ Level = "Medium"; Color = "warning"; Score = $RiskScore } }
    else { return @{ Level = "High"; Color = "danger"; Score = $RiskScore } }
}

function Get-ComplianceColor {
    param($Status)
    switch ($Status) {
        "Compliant" { return "success" }
        "Review Required" { return "warning" }
        "Non-Compliant" { return "danger" }
        default { return "secondary" }
    }
}

function Get-RiskLevelColor {
    param($RiskLevel)
    switch ($RiskLevel) {
        "Critical" { return "danger" }
        "High" { return "warning" }
        "Medium" { return "info" }
        "Low" { return "success" }
        default { return "secondary" }
    }
}

function Format-Finding {
    param($ControlData)
    
    # Extract or generate finding components
    $Title = if ($ControlData.ControlName) { $ControlData.ControlName } else { "Security Control Assessment" }
    $Finding = if ($ControlData.Finding) { $ControlData.Finding } else { "Assessment completed for this security control." }
    
    # Generate root causes (simplified for demo - in real implementation, these would be more sophisticated)
    $RootCauses = @(
        "Configuration settings may not align with security best practices",
        "System may lack proper security hardening measures"
    )
    
    # Generate risks (simplified for demo)
    $Risks = @(
        "Potential security vulnerabilities may exist",
        "System may be susceptible to security threats"
    )
    
    $Recommendation = if ($ControlData.Recommendation) { $ControlData.Recommendation } else { "Review and implement security best practices for this control." }
    
    # Calculate risk rating (using simplified approach)
    $RiskRating = Get-RiskRating -Impact 3 -Possibility 3 -Frequency 2
    
    return @{
        Title = $Title
        Finding = $Finding
        RootCauses = $RootCauses
        Risks = $Risks
        Recommendation = $Recommendation
        RiskRating = $RiskRating
        MeetingBrief = "Discuss implementation timeline and resource requirements with stakeholders"
        BestPractice = "Industry-standard security controls should be implemented and maintained"
    }
}

# Generate current date for report
$ReportDate = Get-Date -Format "MMMM dd, yyyy"
$ReportTime = Get-Date -Format "HH:mm:ss"

# Start building HTML content
Write-Host "Generating comprehensive HTML report..." -ForegroundColor Yellow

# Calculate summary statistics
$SecurityControls = $AuditData.PSObject.Properties | Where-Object { $_.Name -like "WIN_*" }
$TotalControls = $SecurityControls.Count
$CompliantControls = ($SecurityControls | Where-Object { $_.Value.ComplianceStatus -eq "Compliant" }).Count
$ReviewRequiredControls = ($SecurityControls | Where-Object { $_.Value.ComplianceStatus -eq "Review Required" }).Count
$NonCompliantControls = $TotalControls - $CompliantControls - $ReviewRequiredControls

$OverallComplianceRate = if ($TotalControls -gt 0) { [math]::Round(($CompliantControls / $TotalControls) * 100, 1) } else { 0 }

# Categorize findings by risk level
$HighRiskFindings = $SecurityControls | Where-Object { $_.Value.RiskLevel -eq "Critical" -or $_.Value.RiskLevel -eq "High" }
$MediumRiskFindings = $SecurityControls | Where-Object { $_.Value.RiskLevel -eq "Medium" }
$LowRiskFindings = $SecurityControls | Where-Object { $_.Value.RiskLevel -eq "Low" }

Write-Host "Building HTML structure..." -ForegroundColor Yellow

# HTML Document Structure with Enhanced Styling
$HtmlContent = @"
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>$ReportTitle - $CompanyName</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #64748b;
            --success-color: #059669;
            --warning-color: #d97706;
            --danger-color: #dc2626;
            --info-color: #0891b2;
            --light-bg: #f8fafc;
            --border-color: #e2e8f0;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            line-height: 1.6;
            color: #334155;
            background-color: #ffffff;
        }

        .report-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, #1e40af 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }

        .company-logo {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .report-meta {
            background: var(--light-bg);
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .executive-summary {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border-left: 4px solid var(--info-color);
            padding: 2rem;
            margin-bottom: 2rem;
            border-radius: 0.5rem;
        }

        .risk-card {
            border: none;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 1.5rem;
            border-radius: 0.75rem;
            overflow: hidden;
        }

        .risk-high { border-left: 5px solid var(--danger-color); }
        .risk-medium { border-left: 5px solid var(--warning-color); }
        .risk-low { border-left: 5px solid var(--success-color); }

        .finding-card {
            background: white;
            border: 1px solid var(--border-color);
            border-radius: 0.75rem;
            margin-bottom: 2rem;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .finding-header {
            padding: 1.5rem;
            border-bottom: 1px solid var(--border-color);
        }

        .finding-body {
            padding: 1.5rem;
        }

        .risk-rating {
            display: inline-flex;
            align-items: center;
            padding: 0.5rem 1rem;
            border-radius: 2rem;
            font-weight: 600;
            font-size: 0.875rem;
        }

        .risk-rating.high {
            background: #fef2f2;
            color: var(--danger-color);
            border: 1px solid #fecaca;
        }

        .risk-rating.medium {
            background: #fffbeb;
            color: var(--warning-color);
            border: 1px solid #fed7aa;
        }

        .risk-rating.low {
            background: #f0fdf4;
            color: var(--success-color);
            border: 1px solid #bbf7d0;
        }

        .compliance-badge {
            padding: 0.5rem 1rem;
            border-radius: 2rem;
            font-weight: 600;
            font-size: 0.875rem;
        }

        .metric-card {
            text-align: center;
            padding: 2rem 1rem;
            background: white;
            border-radius: 0.75rem;
            border: 1px solid var(--border-color);
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .metric-value {
            font-size: 3rem;
            font-weight: 700;
            line-height: 1;
            margin-bottom: 0.5rem;
        }

        .metric-label {
            color: var(--secondary-color);
            font-weight: 500;
            text-transform: uppercase;
            font-size: 0.875rem;
            letter-spacing: 0.05em;
        }

        .technical-data {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 0.5rem;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
            overflow-x: auto;
        }

        .signature-block {
            background: var(--light-bg);
            border: 2px solid var(--border-color);
            border-radius: 0.5rem;
            padding: 2rem;
            margin-top: 3rem;
            text-align: center;
        }

        .page-break {
            page-break-before: always;
        }

        @media print {
            .no-print { display: none !important; }
            .page-break { page-break-before: always; }
            body { font-size: 12pt; }
            .report-header { background: #2563eb !important; -webkit-print-color-adjust: exact; }
        }

        .nav-pills .nav-link {
            border-radius: 2rem;
            padding: 0.75rem 1.5rem;
            margin-right: 0.5rem;
            font-weight: 500;
        }

        .nav-pills .nav-link.active {
            background-color: var(--primary-color);
        }

        .table-responsive {
            border-radius: 0.5rem;
            overflow: hidden;
            border: 1px solid var(--border-color);
        }

        .table th {
            background-color: var(--light-bg);
            border-color: var(--border-color);
            font-weight: 600;
            color: var(--secondary-color);
            text-transform: uppercase;
            font-size: 0.75rem;
            letter-spacing: 0.05em;
        }

        .progress {
            height: 1rem;
            border-radius: 0.5rem;
        }

        .alert {
            border: none;
            border-radius: 0.75rem;
            padding: 1.5rem;
        }

        .btn {
            border-radius: 0.5rem;
            font-weight: 500;
            padding: 0.75rem 1.5rem;
        }
    </style>
</head>
<body>
    <!-- Report Header -->
    <div class="report-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <div class="company-logo">
                        <i class="fas fa-shield-alt me-3"></i>$CompanyName
                    </div>
                    <h1 class="display-5 mb-0">$ReportTitle</h1>
                    <p class="lead mb-0 opacity-75">Comprehensive Security Assessment Report</p>
                </div>
                <div class="col-md-4 text-md-end">
                    <div class="fs-5 fw-semibold">Report Date</div>
                    <div class="fs-6 opacity-75">$ReportDate at $ReportTime</div>
"@

# Add system information if available
if ($AuditData.SystemIdentity) {
    $SystemId = $AuditData.SystemIdentity
    $HtmlContent += @"
                    <div class="mt-3">
                        <div class="fs-6 fw-semibold">Target System</div>
                        <div class="fs-6 opacity-75">$($SystemId.ComputerName)</div>
                        <div class="fs-6 opacity-75">$($SystemId.Domain)</div>
                    </div>
"@
}

$HtmlContent += @"
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Report Metadata -->
        <div class="report-meta">
            <div class="row">
                <div class="col-md-3">
                    <div class="fw-semibold text-muted mb-1">AUDIT ID</div>
"@

if ($AuditData.AuditMetadata -and $AuditData.AuditMetadata.AuditID) {
    $HtmlContent += @"
                    <div class="font-monospace">$($AuditData.AuditMetadata.AuditID)</div>
"@
} else {
    $HtmlContent += @"
                    <div class="font-monospace">N/A</div>
"@
}

$HtmlContent += @"
                </div>
                <div class="col-md-3">
                    <div class="fw-semibold text-muted mb-1">ASSESSMENT SCOPE</div>
                    <div>$TotalControls Security Controls</div>
                </div>
                <div class="col-md-3">
                    <div class="fw-semibold text-muted mb-1">COMPLIANCE RATE</div>
                    <div class="fw-bold text-$(if($OverallComplianceRate -ge 80){'success'}elseif($OverallComplianceRate -ge 60){'warning'}else{'danger'})">$OverallComplianceRate%</div>
                </div>
                <div class="col-md-3">
                    <div class="fw-semibold text-muted mb-1">REPORT VERSION</div>
                    <div>2.0 - Enhanced</div>
                </div>
            </div>
        </div>

        <!-- Executive Summary -->
        <div class="executive-summary">
            <h2 class="h3 mb-4">
                <i class="fas fa-chart-line me-2"></i>Executive Summary
            </h2>
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="metric-card">
                        <div class="metric-value text-success">$CompliantControls</div>
                        <div class="metric-label">Compliant</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="metric-card">
                        <div class="metric-value text-warning">$ReviewRequiredControls</div>
                        <div class="metric-label">Review Required</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="metric-card">
                        <div class="metric-value text-danger">$NonCompliantControls</div>
                        <div class="metric-label">Non-Compliant</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="metric-card">
                        <div class="metric-value text-primary">$TotalControls</div>
                        <div class="metric-label">Total Controls</div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-8">
                    <h4 class="h5 mb-3">Key Findings</h4>
                    <ul class="list-unstyled">
"@

# Add key findings based on risk levels
if ($HighRiskFindings.Count -gt 0) {
    $HtmlContent += @"
                        <li class="mb-2">
                            <i class="fas fa-exclamation-triangle text-danger me-2"></i>
                            <strong>$($HighRiskFindings.Count) High/Critical Risk</strong> findings require immediate attention
                        </li>
"@
}

if ($MediumRiskFindings.Count -gt 0) {
    $HtmlContent += @"
                        <li class="mb-2">
                            <i class="fas fa-exclamation-circle text-warning me-2"></i>
                            <strong>$($MediumRiskFindings.Count) Medium Risk</strong> findings should be addressed in the next maintenance window
                        </li>
"@
}

if ($LowRiskFindings.Count -gt 0) {
    $HtmlContent += @"
                        <li class="mb-2">
                            <i class="fas fa-info-circle text-info me-2"></i>
                            <strong>$($LowRiskFindings.Count) Low Risk</strong> findings can be addressed as part of routine maintenance
                        </li>
"@
}

$HtmlContent += @"
                    </ul>

                    <h4 class="h5 mb-3 mt-4">Recommendations</h4>
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            Prioritize remediation of high and critical risk findings
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            Implement regular security monitoring and maintenance procedures
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            Schedule quarterly security assessments to maintain compliance
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            Establish incident response procedures for security events
                        </li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h4 class="h5 mb-3">Compliance Overview</h4>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between mb-1">
                            <span class="fw-semibold">Overall Compliance</span>
                            <span class="fw-bold">$OverallComplianceRate%</span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar bg-$(if($OverallComplianceRate -ge 80){'success'}elseif($OverallComplianceRate -ge 60){'warning'}else{'danger'})"
                                 style="width: $OverallComplianceRate%"></div>
                        </div>
                    </div>

                    <div class="alert alert-$(if($OverallComplianceRate -ge 80){'success'}elseif($OverallComplianceRate -ge 60){'warning'}else{'danger'}) mb-0">
                        <div class="fw-semibold mb-1">
"@

if ($OverallComplianceRate -ge 80) {
    $HtmlContent += @"
                            <i class="fas fa-check-circle me-1"></i>Good Compliance Posture
"@
} elseif ($OverallComplianceRate -ge 60) {
    $HtmlContent += @"
                            <i class="fas fa-exclamation-triangle me-1"></i>Moderate Compliance Risk
"@
} else {
    $HtmlContent += @"
                            <i class="fas fa-times-circle me-1"></i>High Compliance Risk
"@
}

$HtmlContent += @"
                        </div>
                        <div class="small">
"@

if ($OverallComplianceRate -ge 80) {
    $HtmlContent += @"
                            The system demonstrates good adherence to security controls with minor areas for improvement.
"@
} elseif ($OverallComplianceRate -ge 60) {
    $HtmlContent += @"
                            Several security controls require attention to improve the overall security posture.
"@
} else {
    $HtmlContent += @"
                            Significant security gaps exist that require immediate remediation efforts.
"@
}

$HtmlContent += @"
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Navigation Tabs -->
        <ul class="nav nav-pills mb-4 no-print" id="reportTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="findings-tab" data-bs-toggle="pill" data-bs-target="#findings" type="button" role="tab">
                    <i class="fas fa-search me-1"></i>Detailed Findings
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="system-tab" data-bs-toggle="pill" data-bs-target="#system" type="button" role="tab">
                    <i class="fas fa-server me-1"></i>System Information
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="technical-tab" data-bs-toggle="pill" data-bs-target="#technical" type="button" role="tab">
                    <i class="fas fa-cogs me-1"></i>Technical Details
                </button>
            </li>
        </ul>

        <!-- Tab Content -->
        <div class="tab-content" id="reportTabsContent">
            <!-- Detailed Findings Tab -->
            <div class="tab-pane fade show active" id="findings" role="tabpanel">
                <h2 class="h3 mb-4">
                    <i class="fas fa-clipboard-list me-2"></i>Detailed Security Findings
                </h2>
"@

# Process each security control and create detailed findings
if ($SecurityControls) {
    # Sort controls by risk level (Critical/High first, then Medium, then Low)
    $SortedControls = $SecurityControls | Sort-Object {
        switch ($_.Value.RiskLevel) {
            "Critical" { 1 }
            "High" { 2 }
            "Medium" { 3 }
            "Low" { 4 }
            default { 5 }
        }
    }

    foreach ($Control in $SortedControls) {
        $ControlData = $Control.Value
        $ControlName = $Control.Name

        # Format the finding using our helper function
        $FormattedFinding = Format-Finding -ControlData $ControlData

        # Get colors and styling
        $ComplianceColor = Get-ComplianceColor -Status $ControlData.ComplianceStatus
        $RiskColor = Get-RiskLevelColor -RiskLevel $ControlData.RiskLevel
        $RiskClass = switch ($ControlData.RiskLevel) {
            "Critical" { "high" }
            "High" { "high" }
            "Medium" { "medium" }
            "Low" { "low" }
            default { "medium" }
        }

        $HtmlContent += @"
                <div class="finding-card risk-$RiskClass">
                    <div class="finding-header">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <h4 class="h5 mb-2">$($FormattedFinding.Title)</h4>
                                <div class="text-muted">Control ID: $ControlName</div>
                            </div>
                            <div class="col-md-4 text-md-end">
                                <div class="mb-2">
                                    <span class="compliance-badge bg-$ComplianceColor text-white">
                                        $($ControlData.ComplianceStatus)
                                    </span>
                                </div>
                                <div class="risk-rating $RiskClass">
                                    <i class="fas fa-exclamation-triangle me-1"></i>
                                    $($ControlData.RiskLevel) Risk
                                    $(if($FormattedFinding.RiskRating.Score) { " (Score: $($FormattedFinding.RiskRating.Score))" })
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="finding-body">
                        <div class="row">
                            <div class="col-md-12 mb-4">
                                <h6 class="fw-semibold text-primary mb-2">
                                    <i class="fas fa-search me-1"></i>Finding (100 words max)
                                </h6>
                                <p class="mb-0">$($FormattedFinding.Finding)</p>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <h6 class="fw-semibold text-warning mb-2">
                                    <i class="fas fa-exclamation-circle me-1"></i>Root Causes (25 words each)
                                </h6>
                                <ol class="mb-0">
"@

        foreach ($RootCause in $FormattedFinding.RootCauses) {
            $HtmlContent += @"
                                    <li class="mb-1">$RootCause</li>
"@
        }

        $HtmlContent += @"
                                </ol>
                            </div>
                            <div class="col-md-6 mb-3">
                                <h6 class="fw-semibold text-danger mb-2">
                                    <i class="fas fa-shield-alt me-1"></i>Risks (25 words each)
                                </h6>
                                <ol class="mb-0">
"@

        foreach ($Risk in $FormattedFinding.Risks) {
            $HtmlContent += @"
                                    <li class="mb-1">$Risk</li>
"@
        }

        $HtmlContent += @"
                                </ol>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <h6 class="fw-semibold text-success mb-2">
                                    <i class="fas fa-lightbulb me-1"></i>Recommendation (100 words max)
                                </h6>
                                <p class="mb-0">$($FormattedFinding.Recommendation)</p>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-2">
                                <h6 class="fw-semibold text-info mb-2">
                                    <i class="fas fa-users me-1"></i>Meeting Brief (20 words max)
                                </h6>
                                <p class="mb-0 small">$($FormattedFinding.MeetingBrief)</p>
                            </div>
                            <div class="col-md-6 mb-2">
                                <h6 class="fw-semibold text-secondary mb-2">
                                    <i class="fas fa-star me-1"></i>Best Practice (20 words, passive voice)
                                </h6>
                                <p class="mb-0 small">$($FormattedFinding.BestPractice)</p>
                            </div>
                        </div>

                        $(if($ControlData.CVSSScore) {
                            "<div class='mt-3 pt-3 border-top'>
                                <div class='row'>
                                    <div class='col-md-4'>
                                        <div class='fw-semibold text-muted mb-1'>CVSS Score</div>
                                        <div class='h5 mb-0'>$($ControlData.CVSSScore)</div>
                                    </div>
                                    <div class='col-md-4'>
                                        <div class='fw-semibold text-muted mb-1'>Current Value</div>
                                        <div class='small'>$($ControlData.CurrentValue)</div>
                                    </div>
                                    <div class='col-md-4'>
                                        <div class='fw-semibold text-muted mb-1'>Baseline Value</div>
                                        <div class='small'>$($ControlData.BaselineValue)</div>
                                    </div>
                                </div>
                            </div>"
                        })
                    </div>
                </div>
"@
    }
}

$HtmlContent += @"
            </div>

            <!-- System Information Tab -->
            <div class="tab-pane fade" id="system" role="tabpanel">
                <h2 class="h3 mb-4">
                    <i class="fas fa-server me-2"></i>System Information & Hardware Fingerprint
                </h2>
"@

# Add System Identity section if available
if ($AuditData.SystemIdentity) {
    $SystemId = $AuditData.SystemIdentity
    $HtmlContent += @"
                <div class="row">
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0">
                                    <i class="fas fa-desktop me-2"></i>System Details
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <tr>
                                            <td class="fw-semibold">Computer Name</td>
                                            <td>$($SystemId.ComputerName)</td>
                                        </tr>
                                        <tr>
                                            <td class="fw-semibold">FQDN</td>
                                            <td>$($SystemId.FQDN)</td>
                                        </tr>
                                        <tr>
                                            <td class="fw-semibold">Domain</td>
                                            <td>$($SystemId.Domain)</td>
                                        </tr>
                                        <tr>
                                            <td class="fw-semibold">Workgroup</td>
                                            <td>$($SystemId.Workgroup)</td>
                                        </tr>
                                        <tr>
                                            <td class="fw-semibold">Domain Role</td>
                                            <td>$($SystemId.DomainRole)</td>
                                        </tr>
                                        <tr>
                                            <td class="fw-semibold">Machine GUID</td>
                                            <td class="font-monospace small">$($SystemId.MachineGUID)</td>
                                        </tr>
                                        <tr>
                                            <td class="fw-semibold">System UUID</td>
                                            <td class="font-monospace small">$($SystemId.SystemUUID)</td>
                                        </tr>
                                        <tr>
                                            <td class="fw-semibold">Serial Number</td>
                                            <td class="font-monospace small">$($SystemId.SerialNumber)</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header bg-warning text-dark">
                                <h5 class="mb-0">
                                    <i class="fas fa-fingerprint me-2"></i>Hardware Fingerprint
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <div class="fw-semibold mb-2">Composite Hardware Fingerprint</div>
                                    <div class="technical-data">$($SystemId.CompositeHardwareFingerprint)</div>
                                </div>
"@

    # Add CPU information if available
    if ($SystemId.HardwareFingerprint -and $SystemId.HardwareFingerprint.CPU) {
        $CPU = $SystemId.HardwareFingerprint.CPU
        $HtmlContent += @"
                                <div class="mb-3">
                                    <div class="fw-semibold mb-2">CPU Information</div>
                                    <div class="small">
                                        <div><strong>Processor ID:</strong> <span class="font-monospace">$($CPU.ProcessorId)</span></div>
                                        <div><strong>Manufacturer:</strong> $($CPU.Manufacturer)</div>
                                        <div><strong>Cores:</strong> $($CPU.NumberOfCores)</div>
                                        <div><strong>Logical Processors:</strong> $($CPU.NumberOfLogicalProcessors)</div>
                                        <div><strong>Max Clock Speed:</strong> $($CPU.MaxClockSpeed) MHz</div>
                                    </div>
                                </div>
"@
    }

    # Add Motherboard information if available
    if ($SystemId.HardwareFingerprint -and $SystemId.HardwareFingerprint.Motherboard) {
        $MB = $SystemId.HardwareFingerprint.Motherboard
        $HtmlContent += @"
                                <div class="mb-3">
                                    <div class="fw-semibold mb-2">Motherboard & BIOS</div>
                                    <div class="small">
                                        <div><strong>Baseboard Serial:</strong> <span class="font-monospace">$($MB.BaseBoardSerialNumber)</span></div>
                                        <div><strong>Baseboard Product:</strong> $($MB.BaseBoardProduct)</div>
                                        <div><strong>BIOS Version:</strong> $($MB.BIOSVersion)</div>
                                        <div><strong>BIOS Manufacturer:</strong> $($MB.BIOSManufacturer)</div>
                                    </div>
                                </div>
"@
    }

    $HtmlContent += @"
                            </div>
                        </div>
                    </div>
                </div>
"@

    # Add Network Adapters if available
    if ($SystemId.NetworkAdapters) {
        $HtmlContent += @"
                <div class="row">
                    <div class="col-12 mb-4">
                        <div class="card">
                            <div class="card-header bg-info text-white">
                                <h5 class="mb-0">
                                    <i class="fas fa-network-wired me-2"></i>Network Configuration
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Adapter Name</th>
                                                <th>MAC Address</th>
                                                <th>Description</th>
                                            </tr>
                                        </thead>
                                        <tbody>
"@

        foreach ($Adapter in $SystemId.NetworkAdapters) {
            $HtmlContent += @"
                                            <tr>
                                                <td>$($Adapter.Name)</td>
                                                <td class="font-monospace">$($Adapter.MacAddress)</td>
                                                <td class="small">$($Adapter.InterfaceDescription)</td>
                                            </tr>
"@
        }

        $HtmlContent += @"
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
"@
    }
}

$HtmlContent += @"
            </div>

            <!-- Technical Details Tab -->
            <div class="tab-pane fade" id="technical" role="tabpanel">
                <h2 class="h3 mb-4">
                    <i class="fas fa-cogs me-2"></i>Technical Details & Audit Integrity
                </h2>
"@

# Add Audit Signature section if available
if ($AuditData.AuditSignature) {
    $AuditSig = $AuditData.AuditSignature
    $HtmlContent += @"
                <div class="row">
                    <div class="col-12 mb-4">
                        <div class="card">
                            <div class="card-header bg-success text-white">
                                <h5 class="mb-0">
                                    <i class="fas fa-certificate me-2"></i>Audit Integrity & Tamper Detection
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="table-responsive">
                                            <table class="table table-sm">
                                                <tr>
                                                    <td class="fw-semibold">Signature Timestamp</td>
                                                    <td>$($AuditSig.SignatureTimestamp)</td>
                                                </tr>
                                                <tr>
                                                    <td class="fw-semibold">Signed By</td>
                                                    <td>$($AuditSig.SignedBy)</td>
                                                </tr>
                                                <tr>
                                                    <td class="fw-semibold">Audit Version</td>
                                                    <td>$($AuditSig.AuditVersion)</td>
                                                </tr>
                                            </table>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
"@

    if ($AuditSig.AuditIntegrityHashes) {
        $Hashes = $AuditSig.AuditIntegrityHashes
        $HtmlContent += @"
                                        <div class="fw-semibold mb-2">Integrity Hashes</div>
                                        <div class="mb-2">
                                            <div class="small fw-semibold">SHA256:</div>
                                            <div class="technical-data">$($Hashes.SHA256)</div>
                                        </div>
                                        <div class="mb-2">
                                            <div class="small fw-semibold">MD5:</div>
                                            <div class="technical-data">$($Hashes.MD5)</div>
                                        </div>
                                        <div class="mb-2">
                                            <div class="small fw-semibold">Data Length:</div>
                                            <div>$($Hashes.DataLength) bytes</div>
                                        </div>
"@
    }

    $HtmlContent += @"
                                    </div>
                                </div>
                                <div class="alert alert-info mt-3">
                                    <div class="fw-semibold mb-1">
                                        <i class="fas fa-shield-alt me-1"></i>Tamper Detection
                                    </div>
                                    <div class="small">
                                        This audit report includes cryptographic integrity hashes that can be used to verify
                                        that the audit results have not been modified since generation. Any tampering with
                                        the source data will be detectable through hash validation.
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
"@
}

# Add detailed technical data if requested
if ($IncludeDetailedTechnicalData) {
    $HtmlContent += @"
                <div class="row">
                    <div class="col-12 mb-4">
                        <div class="card">
                            <div class="card-header bg-secondary text-white">
                                <h5 class="mb-0">
                                    <i class="fas fa-code me-2"></i>Raw Technical Data
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-warning">
                                    <div class="fw-semibold mb-1">
                                        <i class="fas fa-exclamation-triangle me-1"></i>Technical Data
                                    </div>
                                    <div class="small">
                                        The following section contains raw technical data from the audit.
                                        This information is provided for technical review and troubleshooting purposes.
                                    </div>
                                </div>
                                <div class="technical-data" style="max-height: 400px; overflow-y: auto;">
                                    <pre>$($AuditData | ConvertTo-Json -Depth 5)</pre>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
"@
}

$HtmlContent += @"
            </div>
        </div>

        <!-- Page Break for Print -->
        <div class="page-break"></div>

        <!-- E.Z. Consultancy Signature Block -->
        <div class="signature-block">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <div class="text-start">
                        <div class="h4 mb-2">
                            <i class="fas fa-shield-alt me-2 text-primary"></i>
                            <strong>$CompanyName</strong>
                        </div>
                        <div class="mb-2">
                            <strong>Security Audit Report</strong> - Version 2.0
                        </div>
                        <div class="mb-2">
                            <strong>Exchange Version:</strong> Windows Server 2019/2016/2022 Compatible
                        </div>
                        <div class="mb-2">
                            <strong>Internal Audit Authority:</strong> Comprehensive Security Assessment Framework
                        </div>
                        <div class="small text-muted">
                            This report was generated using advanced security auditing methodologies and
                            hardware-level system identification techniques. All findings are based on
                            industry-standard security frameworks and best practices.
                        </div>
                    </div>
                </div>
                <div class="col-md-4 text-end">
                    <div class="mb-2">
                        <div class="fw-semibold">Report Generated</div>
                        <div>$ReportDate</div>
                        <div>$ReportTime</div>
                    </div>
                    <div class="mb-2">
                        <div class="fw-semibold">Document Version</div>
                        <div>2.0 - Enhanced HTML Report</div>
                    </div>
                    <div class="small text-muted">
                        © $(Get-Date -Format yyyy) $CompanyName<br>
                        All Rights Reserved
                    </div>
                </div>
            </div>

            <hr class="my-4">

            <div class="row">
                <div class="col-12 text-center">
                    <div class="small text-muted">
                        <strong>CONFIDENTIAL:</strong> This report contains sensitive security information and should be
                        handled according to your organization's data classification policies. Distribution should be
                        limited to authorized personnel only.
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JavaScript for enhanced functionality -->
    <script>
        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // Print functionality
        function printReport() {
            window.print();
        }

        // Add print button
        document.addEventListener('DOMContentLoaded', function() {
            const header = document.querySelector('.report-header .container');
            if (header) {
                const printBtn = document.createElement('button');
                printBtn.className = 'btn btn-outline-light btn-sm no-print';
                printBtn.innerHTML = '<i class="fas fa-print me-1"></i>Print Report';
                printBtn.onclick = printReport;
                printBtn.style.position = 'absolute';
                printBtn.style.top = '1rem';
                printBtn.style.right = '1rem';
                header.style.position = 'relative';
                header.appendChild(printBtn);
            }
        });
    </script>
</body>
</html>
"@

# Save the HTML file
try {
    Write-Host "Saving comprehensive HTML report..." -ForegroundColor Yellow
    $HtmlContent | Out-File -FilePath $OutputPath -Encoding UTF8 -ErrorAction Stop
    Write-Host "[SUCCESS] Comprehensive HTML report generated: $OutputPath" -ForegroundColor Green

    # Display summary
    Write-Host ""
    Write-Host "REPORT SUMMARY:" -ForegroundColor Yellow
    Write-Host "- Total Security Controls: $TotalControls" -ForegroundColor White
    Write-Host "- Compliant Controls: $CompliantControls" -ForegroundColor Green
    Write-Host "- Review Required: $ReviewRequiredControls" -ForegroundColor Yellow
    Write-Host "- Non-Compliant: $NonCompliantControls" -ForegroundColor Red
    Write-Host "- Overall Compliance Rate: $OverallComplianceRate%" -ForegroundColor Cyan
    Write-Host "- High/Critical Risk Findings: $($HighRiskFindings.Count)" -ForegroundColor Red
    Write-Host "- Medium Risk Findings: $($MediumRiskFindings.Count)" -ForegroundColor Yellow
    Write-Host "- Low Risk Findings: $($LowRiskFindings.Count)" -ForegroundColor Green

} catch {
    Write-Host "[ERROR] Failed to save HTML report: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "=================================================================================" -ForegroundColor Green
Write-Host "Comprehensive HTML Report Generation Complete" -ForegroundColor Green
Write-Host "=================================================================================" -ForegroundColor Green
Write-Host "Output File: $OutputPath" -ForegroundColor Cyan
Write-Host "Company: $CompanyName" -ForegroundColor Cyan
Write-Host "Report Features:" -ForegroundColor Yellow
Write-Host "  ✓ Executive Summary with Key Metrics" -ForegroundColor Green
Write-Host "  ✓ Risk-Based Finding Categorization" -ForegroundColor Green
Write-Host "  ✓ Detailed Findings with Root Causes & Risks" -ForegroundColor Green
Write-Host "  ✓ Professional Styling & Responsive Design" -ForegroundColor Green
Write-Host "  ✓ Hardware Fingerprinting Information" -ForegroundColor Green
Write-Host "  ✓ Audit Integrity Verification" -ForegroundColor Green
Write-Host "  ✓ E.Z. Consultancy Branding & Signature Block" -ForegroundColor Green
Write-Host "  ✓ Print-Friendly Layout" -ForegroundColor Green
Write-Host ""
Write-Host "Open the HTML file in any web browser to view the comprehensive audit report." -ForegroundColor Cyan
Write-Host "=================================================================================" -ForegroundColor Green
