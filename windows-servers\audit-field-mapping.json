{"metadata": {"version": "1.0", "description": "Audit Field Obfuscation Mapping Dictionary", "created": "2025-01-09", "purpose": "Maps obfuscated JSON field names to their actual meanings for audit analysis", "confidentiality": "INTERNAL USE ONLY - Do not share with auditees"}, "section_mappings": {"Section_A7B2": "SystemIdentity", "Meta_X9K1": "AuditSignature", "Guide_P4M8": "ValidationInstructions", "Legacy_D3F5": "AuditMetadata", "Summary_K8L9": "ComplianceSummary", "Control_W1M2": "WIN_1_1_WindowsUpdate", "Control_U2N3": "WIN_2_1_UserAccounts", "Control_F3P4": "WIN_3_1_WindowsFirewall", "Control_L4Q5": "WIN_4_1_AuditLogging", "Control_S5R6": "WIN_5_1_ServiceSecurity", "Control_R6T7": "WIN_6_1_RegistrySecurity", "Control_F7U8": "WIN_7_1_FileSystemSecurity"}, "system_identity_mappings": {"Sys_ID_001": "ComputerName", "Net_ID_002": "FQDN", "Mach_ID_003": "MachineGUID", "UUID_ID_004": "SystemUUID", "Serial_005": "SerialNumber", "Net_Adapt_006": "NetworkAdapters", "IP_List_007": "IPAddresses", "Dom_Role_008": "DomainRole", "Domain_009": "Domain", "WGroup_010": "Workgroup", "HW_Print_011": "HardwareFingerprint", "CHF_Hash_012": "CompositeHardwareFingerprint", "Int_Meta_013": "IntegrityMetadata", "Int_Hash_014": "IntegrityHashes", "Valid_Guide_015": "ValidationInstructions"}, "hardware_fingerprint_mappings": {"HW_CPU_001": "CPU", "HW_MB_002": "Motherboard", "HW_MEM_003": "Memory", "HW_STOR_004": "Storage", "HW_NET_005": "NetworkHardware"}, "cpu_mappings": {"CPU_ID_001": "ProcessorId", "CPU_SIG_002": "Signature", "CPU_FAM_003": "Family", "CPU_MOD_004": "Model", "CPU_STEP_005": "Stepping", "CPU_MFG_006": "Manufacturer", "CPU_CLK_007": "MaxClockSpeed", "CPU_CORE_008": "NumberOfCores", "CPU_LOG_009": "NumberOfLogicalProcessors"}, "motherboard_mappings": {"MB_SN_001": "BaseBoardSerialNumber", "MB_PROD_002": "BaseBoardProduct", "MB_MFG_003": "BaseBoardManufacturer", "MB_VER_004": "BaseBoardVersion", "BIOS_SN_005": "BIOSSerialNumber", "BIOS_VER_006": "BIOSVersion", "BIOS_MFG_007": "BIOSManufacturer", "BIOS_DATE_008": "BIOSReleaseDate"}, "memory_mappings": {"MEM_SN_001": "SerialNumber", "MEM_PN_002": "PartNumber", "MEM_MFG_003": "Manufacturer", "MEM_CAP_004": "Capacity", "MEM_SPD_005": "Speed", "MEM_LOC_006": "DeviceLocator"}, "storage_mappings": {"STOR_SN_001": "SerialNumber", "STOR_MOD_002": "Model", "STOR_MFG_003": "Manufacturer", "STOR_SIZE_004": "Size", "STOR_FW_005": "FirmwareRevision", "STOR_INT_006": "InterfaceType"}, "network_hardware_mappings": {"NET_MAC_001": "MACAddress", "NET_PNP_002": "PNPDeviceID", "NET_MFG_003": "Manufacturer", "NET_PROD_004": "ProductName", "NET_SVC_005": "ServiceName"}, "integrity_metadata_mappings": {"TS_001": "CollectionTimestamp", "BY_002": "Collected<PERSON><PERSON>", "PS_VER_003": "PowerShellVersion", "OS_VER_004": "OSVersion", "OS_BUILD_005": "OSBuildNumber", "INST_DATE_006": "InstallDate", "BOOT_TIME_007": "LastBootUpTime", "TZ_008": "TimeZone", "UPTIME_009": "SystemUptime"}, "integrity_hashes_mappings": {"Hash_SHA256_001": "SHA256", "Hash_SHA512_002": "SHA512", "Hash_MD5_003": "MD5", "Hash_CRC32_004": "CRC32", "Data_Len_005": "DataLength", "Algo_006": "Algorithm", "TS_007": "Timestamp"}, "audit_signature_mappings": {"Sig_Hash_001": "AuditIntegrityHashes", "Sig_TS_002": "SignatureTimestamp", "Sig_By_003": "SignedBy", "Audit_Ver_004": "AuditVersion", "Int_Algo_005": "IntegrityAlgorithms", "Valid_Inst_006": "ValidationInstructions"}, "validation_instructions_mappings": {"Usage_001": "Usage", "Example_002": "Example", "Warning_003": "Warning", "HW_Verify_004": "HardwareVerification"}, "legacy_audit_metadata_mappings": {"Audit_ID_001": "AuditID", "Start_Time_002": "AuditStartTime", "Comp_Name_003": "ComputerName", "Win_Ver_004": "WindowsVersion", "Build_Num_005": "WindowsBuildNumber", "OS_Cap_006": "OSCaption", "OS_Arch_007": "OSArchitecture", "Mem_Total_008": "TotalPhysicalMemory", "Domain_009": "Domain", "WGroup_010": "Workgroup", "Scope_011": "AssessmentScope", "User_012": "AuditUser", "User_Dom_013": "UserDomain"}, "compliance_summary_mappings": {"Assess_Type_001": "AssessmentType", "Total_Ctrl_002": "TotalControlsAssessed", "Success_003": "SuccessfulAssessments", "Failed_004": "FailedAssessments", "Start_005": "AuditStartTime", "End_006": "AuditEndTime", "Duration_007": "AuditDurationSeconds", "ID_008": "AuditID", "Framework_009": "FrameworkVersion", "Type_010": "AuditType", "Exec_By_011": "AuditExecutedBy", "Comp_012": "ComputerName", "PS_Ver_013": "PowerShellVersion"}, "security_control_common_mappings": {"Ctrl_ID_001": "ControlID", "Ctrl_Name_002": "ControlName", "Risk_Level_003": "RiskLevel", "CVSS_004": "CVSSScore", "Current_005": "CurrentValue", "Baseline_006": "BaselineValue", "Compliance_007": "ComplianceStatus", "Score_008": "ComplianceScore", "Finding_009": "Finding", "Recommend_010": "Recommendation", "Assess_Date_011": "AssessmentDate"}}